// page/wardrobe/calendar/calendar.js

// 引入图片管理模块和搭配管理模块
const imageManager = require('../ootd/modules/imageManager');
const outfitManager = require('../ootd/modules/outfitManager');

// 打印outfitManager模块，确认其中包含所需函数
console.log('outfitManager模块:', Object.keys(outfitManager));

// 测试getCompleteOutfitData函数是否可用
console.log('getCompleteOutfitData函数是否可用:', typeof outfitManager.getCompleteOutfitData === 'function');

// 缓存相关常量
const MONTHLY_OUTFITS_CACHE_KEY = 'monthly_outfits_cache_';
const USER_WARDROBES_CACHE_KEY = 'user_wardrobes_';

Page({

  /**
   * 页面的初始数据
   */
  data: {
    // 日历相关数据
    year: new Date().getFullYear(),
    month: new Date().getMonth() + 1,
    days: [],
    weekDays: ['日', '一', '二', '三', '四', '五', '六'],
    firstDayOfMonth: 0,
    selectedDay: null,
    selectedDate: null,
    selectedDateFormatted: '',

    // 主题风格
    themeStyle: 'autumn', // 默认秋季风格
    colors: {
      cowhideCocoa: '#442D1C',   // 深棕色 Cowhide Cocoa
      spicedWine: '#74301C',     // 红棕色 Spiced Wine
      toastedCaramel: '#84592B', // 焦糖色 Toasted Caramel
      oliveHarvest: '#9D9167',   // 橄榄色 Olive Harvest
      goldenBatter: '#EAD8A1',   // 金黄色 Golden Batter
    },
    // 粉蓝色系配色
    pinkBlueColors: {
      pinkDark: '#D47C99',      // 深粉色
      pinkMedium: '#EEA0B2',    // 中粉色
      pinkLight: '#F9C9D6',     // 浅粉色
      blueLight: '#CBE0F9',     // 浅蓝色
      blueMedium: '#97C8E5',    // 中蓝色
      blueDark: '#5EA0D0',      // 深蓝色
    },
    // 黑白主题配色
    blackWhiteColors: {
      black: '#000000',         // 黑色
      darkGray: '#333333',      // 深灰色
      mediumGray: '#666666',    // 中灰色
      lightGray: '#CCCCCC',     // 浅灰色
      offWhite: '#F5F5F5',      // 灰白色
      white: '#FFFFFF',         // 白色
    },
    // 页面样式
    pageStyle: {
      backgroundColor: '',
      backgroundImage: '',
      calendarHeaderBg: '',
      calendarWeekdaysBg: '',
      buttonBg: '',
      todayBg: ''
    },

    // 用户数据
    userOpenId: '',
    isLoading: true,

    // 穿搭历史数据
    monthlyOutfits: {}, // 格式: { '2023-5-15': { outfitId: 'xxx', outfitName: 'xxx', imageUrl: 'xxx' } }
    selectedDateOutfit: null, // 选中日期的穿搭数据

    // 完整穿搭数据缓存
    outfitsCache: {}, // 格式: { 'outfitId': outfitData }

    // 调试模式（开发环境启用）
    debugMode: true
  },

  /**
   * 生命周期函数--监听页面加载
   */
  onLoad: function(options) {
    console.log('Calendar页面 onLoad');

    // 初始化主题
    this.initTheme();

    // 获取用户OpenID
    this.getUserOpenId();

    // 如果有传入特定日期参数，则跳转到该日期
    if (options.year && options.month) {
      const year = parseInt(options.year);
      const month = parseInt(options.month);
      this.setData({
        year: year,
        month: month
      });
    }

    // 初始化日历
    this.initCalendar();
  },

  /**
   * 生命周期函数--监听页面初次渲染完成
   */
  onReady() {

  },

  /**
   * 生命周期函数--监听页面显示
   */
  onShow: function() {
    console.log('Calendar页面 onShow');

    // 检查主题是否有变更
    this.checkTheme();

    // 检查是否需要刷新穿搭数据
    const needRefreshOutfits = wx.getStorageSync('needRefreshOutfits');
    if (needRefreshOutfits) {
      console.log('检测到其他页面修改了穿搭数据，重新加载数据');
      wx.removeStorageSync('needRefreshOutfits');
      this.clearMonthlyOutfitsCache();

      if (this.data.userOpenId) {
        this.getMonthlyOutfits();
      }
    }
  },

  /**
   * 生命周期函数--监听页面隐藏
   */
  onHide() {

  },

  /**
   * 生命周期函数--监听页面卸载
   */
  onUnload() {

  },

  /**
   * 页面相关事件处理函数--监听用户下拉动作
   */
  onPullDownRefresh() {

  },

  /**
   * 页面上拉触底事件的处理函数
   */
  onReachBottom() {

  },

  /**
   * 用户点击右上角分享
   */
  onShareAppMessage() {

  },

  // 获取用户OpenID
  getUserOpenId: function() {
    const that = this;
    wx.showLoading({
      title: '加载中...',
    });

    // 尝试从本地缓存获取OpenID
    const openid = wx.getStorageSync('openid');
    if (openid) {
      console.log('从本地缓存获取到OpenID:', openid);
      that.setData({
        userOpenId: openid
      });

      // 获取当月穿搭记录
      that.getMonthlyOutfits();
      wx.hideLoading();
      return;
    }

    // 如果本地没有，则调用云函数获取
    wx.cloud.callFunction({
      name: 'login',
      data: {},
      success: res => {
        console.log('获取用户OpenID成功:', res.result);
        const openid = res.result.openid;

        // 成功获取到openid
        if (openid) {
          // 存入本地缓存
          wx.setStorageSync('openid', openid);

          that.setData({
            userOpenId: openid
          });

          // 获取当月穿搭记录
          that.getMonthlyOutfits();
        } else {
          wx.showToast({
            title: '获取用户信息失败',
            icon: 'none'
          });
        }
      },
      fail: err => {
        console.error('云函数调用失败:', err);
        wx.showToast({
          title: '获取用户信息失败',
          icon: 'none'
        });
      },
      complete: () => {
        wx.hideLoading();
      }
    });
  },

  // 初始化主题设置
  initTheme: function() {
    const savedTheme = wx.getStorageSync('themeStyle');
    console.log('从存储中读取的主题:', savedTheme);

    if (savedTheme) {
      console.log('应用已保存的主题:', savedTheme);
      this.setData({
        themeStyle: savedTheme
      }, () => {
        // 应用主题样式
        this.applyThemeStyle(savedTheme);
      });
    } else {
      // 应用默认主题样式
      this.applyThemeStyle('autumn');
    }
  },

  // 检查主题是否变更
  checkTheme: function() {
    const savedTheme = wx.getStorageSync('themeStyle');
    if (savedTheme && savedTheme !== this.data.themeStyle) {
      console.log('检测到主题变化，从', this.data.themeStyle, '变为', savedTheme);
      this.setData({
        themeStyle: savedTheme
      }, () => {
        // 应用主题样式
        this.applyThemeStyle(savedTheme);
      });
    }
  },

  // 应用主题样式
  applyThemeStyle: function(theme) {
    console.log('应用主题样式:', theme);

    let pageStyle = {};

    if (theme === 'autumn') {
      // 秋季主题样式
      pageStyle = {
        backgroundColor: '#E8D1A7',
        backgroundImage: '',
        calendarHeaderBg: this.data.colors.toastedCaramel,
        calendarWeekdaysBg: this.data.colors.oliveHarvest,
        buttonBg: this.data.colors.toastedCaramel,
        todayBg: this.data.colors.toastedCaramel
      };
    } else if (theme === 'pinkBlue') {
      // 粉蓝主题样式
      pageStyle = {
        backgroundColor: this.data.pinkBlueColors.pinkLight,
        backgroundImage: 'linear-gradient(to bottom, white, ' + this.data.pinkBlueColors.pinkLight + ')',
        calendarHeaderBg: this.data.pinkBlueColors.pinkDark,
        calendarWeekdaysBg: this.data.pinkBlueColors.blueMedium,
        buttonBg: this.data.pinkBlueColors.pinkDark,
        todayBg: this.data.pinkBlueColors.blueDark
      };
    } else if (theme === 'blackWhite') {
      // 黑白主题样式
      pageStyle = {
        backgroundColor: '#dee3ec',
        backgroundImage: '',
        calendarHeaderBg: this.data.blackWhiteColors.black,
        calendarWeekdaysBg: this.data.blackWhiteColors.mediumGray,
        buttonBg: this.data.blackWhiteColors.black,
        todayBg: this.data.blackWhiteColors.black
      };
    }

    this.setData({
      pageStyle: pageStyle
    });
  },

  // 初始化日历
  initCalendar: function() {
    // 生成日历数据
    this.generateCalendarData();

    // 获取当前日期
    const today = new Date();
    const currentMonth = today.getMonth() + 1;
    const currentYear = today.getFullYear();

    // 如果是当月，自动选择今天
    if (this.data.month === currentMonth && this.data.year === currentYear) {
      this.selectToday();
    }
  },

  // 上个月
  prevMonth: function() {
    let { year, month } = this.data;

    if (month === 1) {
      year--;
      month = 12;
    } else {
      month--;
    }

    this.setData({
      year,
      month,
      selectedDay: null,
      selectedDate: null,
      selectedDateOutfit: null
    }, () => {
      this.initCalendar();
    });
  },

  // 下个月
  nextMonth: function() {
    let { year, month } = this.data;

    if (month === 12) {
      year++;
      month = 1;
    } else {
      month++;
    }

    this.setData({
      year,
      month,
      selectedDay: null,
      selectedDate: null,
      selectedDateOutfit: null
    }, () => {
      this.initCalendar();
    });
  },

  // 跳转到今天
  jumpToToday: function() {
    const today = new Date();

    this.setData({
      year: today.getFullYear(),
      month: today.getMonth() + 1,
      selectedDay: null,
      selectedDate: null,
      selectedDateOutfit: null
    }, () => {
      this.initCalendar();

      // 如果是刚刚复制了穿搭到今天，我们需要特殊处理
      if (this.data.justCopiedOutfit && this.data.justCopiedOutfitId) {
        console.log('检测到刚刚复制了穿搭到今天，处理图片加载');

        // 获取今天的日期字符串
        const todayString = `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`;

        // 延迟执行，确保日历已经初始化完成
        setTimeout(() => {
          // 模拟点击今天
          this.selectDate({
            currentTarget: {
              dataset: {
                date: todayString
              }
            }
          });

          // 选择今天后，清除标记
          this.setData({
            justCopiedOutfit: false,
            justCopiedOutfitId: null
          });
        }, 300);
      } else {
        // 自动选择今天
        this.selectToday();
      }
    });
  },

  // 自动选择今天
  selectToday: function() {
    const today = new Date();
    const currentDay = today.getDate();

    // 检查是否是当前显示的月份
    if (this.data.year === today.getFullYear() && this.data.month === today.getMonth() + 1) {
      // 找到今天的索引
      const days = this.data.days;
      for (let i = 0; i < days.length; i++) {
        if (days[i].day === currentDay) {
          // 模拟点击今天
          this.selectDate({
            currentTarget: {
              dataset: {
                date: days[i].date
              }
            }
          });
          break;
        }
      }
    }
  },

  // 选择日期
  selectDate: function(e) {
    const dateStr = e.currentTarget.dataset.date;
    console.log('选择日期:', dateStr);

    // 解析日期字符串
    const [year, month, day] = dateStr.split('-').map(Number);

    // 更新选中日期
    this.setData({
      selectedDay: day,
      selectedDate: dateStr,
      selectedDateFormatted: `${year}年${month}月${day}日`
    });

    // 检查这是否是今天的日期，以及我们是否刚刚复制了穿搭
    const today = new Date();
    const isToday = (year === today.getFullYear() && month === today.getMonth() + 1 && day === today.getDate());
    const justCopied = this.data.justCopiedOutfit && this.data.justCopiedOutfitId && isToday;

    if (justCopied) {
      console.log('检测到选择的是今天，且刚刚复制了穿搭，使用缓存的穿搭数据');
      const outfitId = this.data.justCopiedOutfitId;
      const outfitDetail = this.data.outfitsCache[outfitId];

      if (outfitDetail) {
        // 如果有缓存的图片URL，直接使用
        if (this.data.justCopiedImageUrl) {
          console.log('使用缓存的图片URL:', this.data.justCopiedImageUrl);
          this.setData({
            selectedDateOutfit: {
              outfitId: outfitId,
              outfitName: outfitDetail.name || '穿搭',
              outfitType: outfitDetail.type || outfitDetail.category || 'daily',
              imageUrl: this.data.justCopiedImageUrl,
              outfit: outfitDetail
            },
            // 清除标记，防止下次选择时出问题
            justCopiedOutfit: false,
            justCopiedOutfitId: null,
            justCopiedImageUrl: null
          });
          return; // 直接返回，不继续执行下面的代码
        }

        // 获取图片临时URL
        this.getOutfitImageUrl(outfitDetail)
          .then(imageUrl => {
            console.log('获取到复制的穿搭图片URL:', imageUrl);
            // 更新选中的穿搭数据
            this.setData({
              selectedDateOutfit: {
                outfitId: outfitId,
                outfitName: outfitDetail.name || '穿搭',
                outfitType: outfitDetail.type || outfitDetail.category || 'daily',
                imageUrl: imageUrl,
                outfit: outfitDetail
              },
              // 清除标记，防止下次选择时出问题
              justCopiedOutfit: false,
              justCopiedOutfitId: null
            });
          })
          .catch(err => {
            console.error('获取穿搭图片URL失败:', err);
            // 即使获取图片失败，也显示穿搭信息
            this.setData({
              selectedDateOutfit: {
                outfitId: outfitId,
                outfitName: outfitDetail.name || '穿搭',
                outfitType: outfitDetail.type || outfitDetail.category || 'daily',
                outfit: outfitDetail
              },
              // 清除标记，防止下次选择时出问题
              justCopiedOutfit: false,
              justCopiedOutfitId: null
            });
          });
        return; // 直接返回，不继续执行下面的代码
      }
    }

    // 查找该日期是否有穿搭记录
    const outfitData = this.data.monthlyOutfits[dateStr];
    console.log('选择日期的穿搭数据:', outfitData);

    if (outfitData) {
      // 检查是否是多件衣物组合
      if (outfitData.isMultipleClothings === true && outfitData.clothingIds && outfitData.clothingIds.length > 0) {
        console.log('检测到多件衣物组合作为穿搭:', outfitData.clothingIds);
        console.log('多件衣物详情:', outfitData.outfitItems);

        // 获取多件衣物的图片URLs
        this.getMultipleClothingImageUrls(outfitData.clothingIds, outfitData.outfitItems)
          .then(clothingItems => {
            console.log('获取到多件衣物图片URLs:', clothingItems);
            // 更新选中的穿搭数据
            this.setData({
              selectedDateOutfit: {
                ...outfitData,
                clothingItems: clothingItems,
                isMultipleClothings: true
              }
            });
          })
          .catch(err => {
            console.error('获取多件衣物图片URLs失败:', err);
            // 即使获取图片失败，也显示基本信息
            this.setData({
              selectedDateOutfit: {
                ...outfitData,
                isMultipleClothings: true,
                clothingItems: outfitData.outfitItems || []
              }
            });
          });
      }
      // 检查是否是单件衣物作为穿搭
      else if (outfitData.isSingleClothing === true && outfitData.singleClothingId) {
        console.log('检测到单件衣物作为穿搭:', outfitData.singleClothingId);
        console.log('单件衣物图片ID:', outfitData.clothingImageFileID);

        // 获取单件衣物图片URL
        this.getClothingImageUrl(outfitData.singleClothingId, outfitData.clothingImageFileID)
          .then(imageUrl => {
            console.log('获取到单件衣物图片URL:', imageUrl);
            // 更新选中的穿搭数据
            this.setData({
              selectedDateOutfit: {
                ...outfitData,
                imageUrl: imageUrl,
                isSingleClothing: true
              }
            });
          })
          .catch(err => {
            console.error('获取单件衣物图片URL失败:', err);
            // 即使获取图片失败，也显示基本信息
            this.setData({
              selectedDateOutfit: {
                ...outfitData,
                isSingleClothing: true
              }
            });
          });
      } else {
        // 常规穿搭记录，获取详细信息
        const outfitId = outfitData.outfitId;
        const outfitDetail = this.data.outfitsCache[outfitId];
        console.log('常规穿搭记录:', outfitId, '详情:', outfitDetail);

        if (outfitDetail) {
          // 获取图片临时URL
          this.getOutfitImageUrl(outfitDetail)
            .then(imageUrl => {
              // 更新选中的穿搭数据
              this.setData({
                selectedDateOutfit: {
                  ...outfitData,
                  imageUrl: imageUrl,
                  outfit: outfitDetail,
                  isSingleClothing: false
                }
              });
            })
            .catch(err => {
              console.error('获取穿搭图片URL失败:', err);

              // 即使获取图片失败，也显示穿搭信息
              this.setData({
                selectedDateOutfit: {
                  ...outfitData,
                  outfit: outfitDetail,
                  isSingleClothing: false
                }
              });
            });
        } else {
          // 找不到穿搭详情，只显示基本信息
          console.log('找不到穿搭详情，只显示基本信息');
          this.setData({
            selectedDateOutfit: {
              ...outfitData,
              isSingleClothing: false
            }
          });
        }
      }
    } else {
      // 没有穿搭记录，清空选中状态
      console.log('没有穿搭记录，清空选中状态');
      this.setData({
        selectedDateOutfit: null
      });
    }
  },

  // 获取月度穿搭记录
  getMonthlyOutfits: function() {
    const that = this;
    const { userOpenId, year, month } = this.data;

    if (!userOpenId) {
      console.warn('用户OpenID不能为空');
      return;
    }

    // 显示加载状态
    that.setData({ isLoading: true });

    // 尝试从缓存获取穿搭记录
    const cacheKey = `${MONTHLY_OUTFITS_CACHE_KEY}${userOpenId}_${year}_${month}`;
    const cachedData = wx.getStorageSync(cacheKey);

    if (cachedData && cachedData.monthlyOutfits) {
      console.log('从缓存获取月度穿搭记录:', cachedData);
      that.setData({
        monthlyOutfits: cachedData.monthlyOutfits,
        outfitsCache: cachedData.outfitsCache || {}
      }, () => {
        that.updateDaysWithOutfits();

        // 如果有选中的日期，重新加载该日期的穿搭
        if (that.data.selectedDate) {
          that.loadOutfitForDate(that.data.selectedDate);
        }

        that.setData({ isLoading: false });
      });
      return;
    }

    // 从云端获取穿搭记录
    wx.showLoading({ title: '加载穿搭记录...' });

    // 计算月份的起止日期 - 使用更宽松的日期范围来避免时区问题
    const startDate = new Date(year, month - 1, 1); // 当月第一天
    startDate.setHours(0, 0, 0, 0);
    // 向前扩展一天以防时区问题
    startDate.setDate(startDate.getDate() - 1);

    const endDate = new Date(year, month, 0); // 当月最后一天
    endDate.setHours(23, 59, 59, 999);
    // 向后扩展一天以防时区问题
    endDate.setDate(endDate.getDate() + 1);

    console.log('获取月度穿搭记录:', '开始日期:', startDate, '结束日期:', endDate);
    console.log('查询月份:', year, '年', month, '月');

    const db = wx.cloud.database();
    const _ = db.command;

    // 查询当月的每日穿搭记录
    db.collection('dailyOutfits')
      .where({
        _openid: userOpenId,
        date: _.gte(startDate).and(_.lte(endDate))
      })
      .get()
      .then(res => {
        console.log('获取月度穿搭记录成功:', res.data);
        console.log('查询到的记录数量:', res.data.length);

        // 打印每条记录的详细信息用于调试
        res.data.forEach((item, index) => {
          console.log(`记录 ${index + 1}:`, {
            id: item._id,
            outfitId: item.outfitId,
            date: item.date,
            dateType: typeof item.date,
            outfitName: item.outfitName,
            isSingleClothing: item.isSingleClothing,
            isMultipleClothings: item.isMultipleClothings
          });
        });

        // 如果没有记录
        if (!res.data || res.data.length === 0) {
          console.log('本月没有穿搭记录');
          that.setData({
            monthlyOutfits: {},
            isLoading: false
          });
          wx.hideLoading();
          return;
        }

        // 提取每日穿搭记录中的所有outfitId，但排除多件衣物和单件衣物的记录
        const outfitIds = [...new Set(res.data
          .filter(item => !item.isSingleClothing && !item.isMultipleClothings)
          .map(item => item.outfitId)
        )];

        // 检查是否有单件衣物或多件衣物作为穿搭
        const hasSingleClothing = res.data.some(item => item.isSingleClothing && item.singleClothingId);
        const hasMultipleClothings = res.data.some(item => item.isMultipleClothings && item.clothingIds && item.clothingIds.length > 0);
        console.log('是否有单件衣物作为穿搭:', hasSingleClothing);
        console.log('是否有多件衣物作为穿搭:', hasMultipleClothings);
        console.log('需要查询的常规穿搭ID:', outfitIds);

        // 获取这些穿搭的详细信息（只查询常规穿搭）
        if (outfitIds.length > 0) {
          that.getOutfitDetails(outfitIds)
            .then(outfitsData => {
              // 将outfitsData转换为以id为键的对象，方便查找
              const outfitsCache = {};
              outfitsData.forEach(outfit => {
                const outfitId = outfit._id || outfit.id;
                if (outfitId) {
                  outfitsCache[outfitId] = outfit;
                }
              });

              // 处理所有穿搭记录
              that.processOutfitRecords(res.data, outfitsCache, year, month);
            });
        } else {
          // 即使没有常规穿搭，也要处理单件衣物和多件衣物的记录
          console.log('没有常规穿搭需要查询，直接处理单件衣物和多件衣物记录');
          that.processOutfitRecords(res.data, {}, year, month);
        }
      })
      .catch(err => {
        console.error('获取穿搭记录失败:', err);
        that.setData({ isLoading: false });
        wx.hideLoading();

        wx.showToast({
          title: '获取记录失败',
          icon: 'none'
        });
      });
  },

  // 处理穿搭记录数据
  processOutfitRecords: function(dailyOutfitRecords, outfitsCache, targetYear, targetMonth) {
    const that = this;
    const monthlyOutfits = {};
    const cacheKey = `${MONTHLY_OUTFITS_CACHE_KEY}${this.data.userOpenId}_${this.data.year}_${this.data.month}`;

    console.log('开始处理穿搭记录:', dailyOutfitRecords.length, '条记录');
    console.log('目标年月:', targetYear, '年', targetMonth, '月');

    // 处理每日穿搭记录
    dailyOutfitRecords.forEach((item, index) => {
      console.log(`处理记录 ${index + 1}:`, item);

      // 使用日期的YYYY-MM-DD格式作为键
      let dateStr;
      let parsedDate;

      if (item.date instanceof Date) {
        parsedDate = item.date;
        dateStr = that.getDateString(parsedDate);
      } else if (typeof item.date === 'string') {
        // 如果已经是字符串，尝试解析
        try {
          parsedDate = new Date(item.date);
          if (isNaN(parsedDate.getTime())) {
            // 如果解析失败，直接使用字符串
            dateStr = item.date;
            console.warn('日期字符串解析失败，直接使用:', item.date);
          } else {
            dateStr = that.getDateString(parsedDate);
          }
        } catch (e) {
          dateStr = item.date;
          console.warn('日期字符串解析异常，直接使用:', item.date);
        }
      } else if (item.date && item.date._seconds) {
        // 处理Firestore时间戳
        parsedDate = new Date(item.date._seconds * 1000);
        dateStr = that.getDateString(parsedDate);
      } else if (item.date && typeof item.date === 'object') {
        // 处理其他日期对象格式
        try {
          parsedDate = new Date(item.date);
          if (!isNaN(parsedDate.getTime())) {
            dateStr = that.getDateString(parsedDate);
          }
        } catch (e) {
          console.warn('无法解析日期对象:', item.date);
        }
      }

      console.log('日期处理结果:', {
        原始日期: item.date,
        解析后日期: parsedDate,
        日期字符串: dateStr
      });

      // 确保我们有一个有效的日期字符串
      if (!dateStr) {
        console.warn('无法获取有效的日期字符串:', item);
        return;
      }

      // 如果有解析后的日期，检查是否属于目标月份
      if (parsedDate && targetYear && targetMonth) {
        const recordYear = parsedDate.getFullYear();
        const recordMonth = parsedDate.getMonth() + 1;

        if (recordYear !== targetYear || recordMonth !== targetMonth) {
          console.log('记录不属于目标月份，跳过:', {
            记录日期: `${recordYear}-${recordMonth}`,
            目标月份: `${targetYear}-${targetMonth}`,
            dateStr: dateStr
          });
          return;
        }
      }

      // 获取穿搭ID
      const outfitId = item.outfitId;
      if (!outfitId) {
        console.warn('穿搭记录缺少outfitId:', item);
        return;
      }

      console.log('处理穿搭记录:', {
        日期: dateStr,
        穿搭ID: outfitId,
        是否单件衣物: item.isSingleClothing,
        是否多件衣物: item.isMultipleClothings
      });

      // 检查是否是多件衣物组合
      if (item.isMultipleClothings && item.clothingIds && item.clothingIds.length > 0) {
        console.log('检测到多件衣物组合作为穿搭:', item.clothingIds);
        // 多件衣物组合作为穿搭
        monthlyOutfits[dateStr] = {
          outfitId: outfitId,
          date: item.date,
          outfitName: item.outfitName || `组合穿搭 (${item.clothingIds.length}件)`,
          outfitType: item.outfitType || 'daily',
          isMultipleClothings: true,
          clothingIds: item.clothingIds,
          outfitItems: item.outfitItems || []
        };
      }
      // 检查是否是单件衣物
      else if (item.isSingleClothing && item.singleClothingId) {
        console.log('检测到单件衣物作为穿搭:', item.singleClothingId);
        // 单件衣物作为穿搭
        monthlyOutfits[dateStr] = {
          outfitId: outfitId,
          date: item.date,
          outfitName: item.outfitName || '单件衣物',
          outfitType: item.outfitType || 'daily',
          isSingleClothing: true,
          singleClothingId: item.singleClothingId,
          clothingImageFileID: item.clothingImageFileID
        };
      } else {
        // 获取穿搭详情
        const outfitDetail = outfitsCache[outfitId];
        if (!outfitDetail) {
          console.warn('找不到穿搭详情:', outfitId);
          // 即使找不到穿搭详情，也添加基本信息
          monthlyOutfits[dateStr] = {
            outfitId: outfitId,
            date: item.date,
            outfitName: item.outfitName || '穿搭',
            outfitType: item.outfitType || 'daily',
            isSingleClothing: false,
            isMultipleClothings: false
          };
        } else {
          // 常规穿搭记录
          monthlyOutfits[dateStr] = {
            outfitId: outfitId,
            date: item.date,
            outfitName: outfitDetail.name || '穿搭',
            outfitType: outfitDetail.category || outfitDetail.type || 'daily',
            imageFileID: outfitDetail.previewImageFileID || outfitDetail.imageFileID,
            isSingleClothing: false,
            isMultipleClothings: false
          };
        }
      }
    });

    console.log('处理后的穿搭记录:', monthlyOutfits);

    // 保存到本地缓存
    wx.setStorageSync(cacheKey, {
      monthlyOutfits: monthlyOutfits,
      outfitsCache: outfitsCache,
      timestamp: Date.now()
    });

    // 更新数据
    that.setData({
      monthlyOutfits: monthlyOutfits,
      outfitsCache: outfitsCache,
      isLoading: false
    }, () => {
      // 更新日历上的穿搭标记
      that.updateDaysWithOutfits();

      // 如果有选中的日期，重新加载该日期的穿搭
      if (that.data.selectedDate) {
        that.loadOutfitForDate(that.data.selectedDate);
      }
    });

    wx.hideLoading();
  },

  // 更新日历上的穿搭标记
  updateDaysWithOutfits: function() {
    console.log('开始更新日历上的穿搭标记');
    const { days, monthlyOutfits } = this.data;

    if (!days || !monthlyOutfits) {
      console.log('无法更新日历标记，数据不完整:', {
        hasDays: !!days,
        hasMonthlyOutfits: !!monthlyOutfits
      });
      return;
    }

    console.log('当前月份穿搭记录:', monthlyOutfits);

    // 如果是今天，先获取最新的今日穿搭记录
    const today = new Date();
    const todayString = `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`;

    // 检查今天是否有穿搭（使用修复后的getTodayOutfit函数）
    outfitManager.getTodayOutfit(this.data.userOpenId)
      .then(todayOutfit => {
        // 判断是否有穿搭记录
        const hasOutfit = todayOutfit !== null;
        let todayOutfitId = null;

        if (hasOutfit) {
          console.log('获取到今日穿搭记录:', todayOutfit);
          todayOutfitId = todayOutfit.outfitId;
          console.log('今天的穿搭 ID:', todayOutfitId);

          // 打印日期信息以确认这确实是今天的记录
          const outfitDate = new Date(todayOutfit.date);
          console.log('穿搭记录的日期:', outfitDate, '今天日期:', today);

          // 更新monthlyOutfits中今天的数据
          if (monthlyOutfits[todayString]) {
            monthlyOutfits[todayString].outfitId = todayOutfitId;
            console.log('更新monthlyOutfits中今天的数据:', monthlyOutfits[todayString]);
          }
        }

        // 更新日期的穿搭标记
        const updatedDays = days.map(day => {
          const dateStr = day.date;
          const outfitData = monthlyOutfits[dateStr];

          // 如果是今天且有最新的穿搭 ID，使用最新的
          if (day.isToday && hasOutfit && todayOutfitId) {
            console.log('今天的日期字符串:', dateStr);
            console.log('使用最新的今日穿搭 ID:', todayOutfitId);

            return {
              ...day,
              hasOutfit: true,
              outfitId: todayOutfitId
            };
          }

          // 其他日期使用monthlyOutfits中的数据
          return {
            ...day,
            hasOutfit: !!outfitData,
            outfitId: outfitData ? outfitData.outfitId : null
          };
        });

        console.log('更新后的日历数据:', updatedDays);

        this.setData({ days: updatedDays });
      })
      .catch(err => {
        console.error('获取今天穿搭失败:', err);

        // 即使获取失败，也继续更新日历
        // 更新日期的穿搭标记
        const updatedDays = days.map(day => {
          const dateStr = day.date;
          const outfitData = monthlyOutfits[dateStr];

          // 如果是今天，打印详细日志
          if (day.isToday) {
            console.log('今天的日期字符串:', dateStr);
            console.log('今天的穿搭数据:', outfitData);
          }

          return {
            ...day,
            hasOutfit: !!outfitData,
            outfitId: outfitData ? outfitData.outfitId : null
          };
        });

        console.log('更新后的日历数据:', updatedDays);

        this.setData({ days: updatedDays });
      });
  },

  // 获取穿搭详情
  getOutfitDetails: function(outfitIds) {
    return new Promise((resolve, reject) => {
      if (!outfitIds || outfitIds.length === 0) {
        resolve([]);
        return;
      }

      const db = wx.cloud.database();
      const _ = db.command;

      db.collection('outfits')
        .where({
          _id: _.in(outfitIds)
        })
        .get()
        .then(res => {
          console.log('获取穿搭详情成功:', res.data);
          resolve(res.data || []);
        })
        .catch(err => {
          console.error('获取穿搭详情失败:', err);
          reject(err);
        });
    });
  },

  // 获取穿搭图片URL
  getOutfitImageUrl: function(outfit) {
    return new Promise((resolve, reject) => {
      if (!outfit) {
        console.error('穿搭数据为空，无法获取图片URL');
        reject(new Error('穿搭数据为空'));
        return;
      }

      console.log('获取穿搭图片URL，穿搭数据:', JSON.stringify({
        id: outfit._id || outfit.id,
        name: outfit.name,
        hasPreviewImage: !!outfit.previewImage,
        hasImageFileID: !!outfit.imageFileID,
        previewImageType: typeof outfit.previewImage
      }));

      // 如果已有可用的预览图，直接使用
      if (outfit.previewImage && !outfit.previewImage.includes('cloud://')) {
        console.log('使用已有的预览图URL:', outfit.previewImage);
        resolve(outfit.previewImage);
        return;
      }

      // 尝试使用previewImageFileID
      if (outfit.previewImageFileID && outfit.previewImageFileID.includes('cloud://')) {
        console.log('尝试使用previewImageFileID获取临时URL:', outfit.previewImageFileID);
        imageManager.refreshTempFileURL(outfit.previewImageFileID, tempURL => {
          if (tempURL) {
            console.log('成功获取previewImageFileID的临时URL:', tempURL);
            resolve(tempURL);
          } else {
            console.warn('获取previewImageFileID的临时URL失败，尝试其他方式');
            // 如果失败，尝试使用imageFileID
            tryWithImageFileID();
          }
        });
      } else {
        // 如果没有previewImageFileID，尝试使用imageFileID
        tryWithImageFileID();
      }

      // 使用imageFileID尝试获取临时URL
      function tryWithImageFileID() {
        if (outfit.imageFileID && outfit.imageFileID.includes('cloud://')) {
          console.log('尝试使用imageFileID获取临时URL:', outfit.imageFileID);
          imageManager.refreshTempFileURL(outfit.imageFileID, tempURL => {
            if (tempURL) {
              console.log('成功获取imageFileID的临时URL:', tempURL);
              resolve(tempURL);
            } else {
              console.warn('获取imageFileID的临时URL失败，使用默认图片');
              // 如果获取失败，使用默认图片
              resolve('/image/outfit-icon.png');
            }
          });
        } else {
          console.log('没有可用的图片ID，使用默认图片');
          // 没有图片ID，使用默认图片
          resolve('/image/outfit-icon.png');
        }
      }
    });
  },

  // 获取多件衣物图片URLs
  getMultipleClothingImageUrls: function(clothingIds, outfitItems) {
    return new Promise((resolve, reject) => {
      if (!clothingIds || clothingIds.length === 0) {
        console.error('衣物ID列表为空，无法获取图片URLs');
        reject(new Error('衣物ID列表为空'));
        return;
      }

      console.log('获取多件衣物图片URLs，参数:', {
        clothingIds: clothingIds,
        outfitItems: outfitItems
      });

      // 如果有outfitItems且包含图片信息，优先使用
      if (outfitItems && outfitItems.length > 0) {
        const promises = outfitItems.map(item => {
          return new Promise((itemResolve) => {
            if (item.imageFileID && item.imageFileID.includes('cloud://')) {
              imageManager.refreshTempFileURL(item.imageFileID, tempURL => {
                itemResolve({
                  ...item,
                  imageUrl: tempURL || '/image/short-dress.png'
                });
              });
            } else if (item.imageUrl) {
              itemResolve({
                ...item,
                imageUrl: item.imageUrl
              });
            } else {
              itemResolve({
                ...item,
                imageUrl: '/image/short-dress.png'
              });
            }
          });
        });

        Promise.all(promises)
          .then(clothingItems => {
            console.log('成功获取多件衣物图片URLs:', clothingItems);
            resolve(clothingItems);
          })
          .catch(err => {
            console.error('获取多件衣物图片URLs失败:', err);
            reject(err);
          });
      } else {
        // 如果没有outfitItems，从数据库获取衣物信息
        const db = wx.cloud.database();
        const _ = db.command;

        db.collection('clothes')
          .where({
            _id: _.in(clothingIds)
          })
          .get()
          .then(res => {
            console.log('从数据库获取衣物信息成功:', res.data);

            const promises = res.data.map(clothing => {
              return new Promise((itemResolve) => {
                const imageFileID = clothing.processedImageFileID || clothing.imageFileID;
                if (imageFileID && imageFileID.includes('cloud://')) {
                  imageManager.refreshTempFileURL(imageFileID, tempURL => {
                    itemResolve({
                      id: clothing._id,
                      name: clothing.name,
                      category: clothing.category || clothing.type,
                      imageFileID: imageFileID,
                      imageUrl: tempURL || '/image/short-dress.png'
                    });
                  });
                } else {
                  itemResolve({
                    id: clothing._id,
                    name: clothing.name,
                    category: clothing.category || clothing.type,
                    imageFileID: imageFileID,
                    imageUrl: '/image/short-dress.png'
                  });
                }
              });
            });

            Promise.all(promises)
              .then(clothingItems => {
                console.log('成功获取多件衣物图片URLs:', clothingItems);
                resolve(clothingItems);
              })
              .catch(err => {
                console.error('获取多件衣物图片URLs失败:', err);
                reject(err);
              });
          })
          .catch(err => {
            console.error('从数据库获取衣物信息失败:', err);
            reject(err);
          });
      }
    });
  },

  // 获取单件衣物图片URL
  getClothingImageUrl: function(clothingId, clothingImageFileID) {
    return new Promise((resolve, reject) => {
      if (!clothingId && !clothingImageFileID) {
        console.error('衣物ID和图片ID都为空，无法获取图片URL');
        reject(new Error('衣物ID和图片ID都为空'));
        return;
      }

      console.log('获取单件衣物图片URL，参数:', {
        clothingId: clothingId,
        clothingImageFileID: clothingImageFileID
      });

      // 如果有图片ID，直接使用
      if (clothingImageFileID && clothingImageFileID.includes('cloud://')) {
        console.log('使用clothingImageFileID获取临时URL:', clothingImageFileID);
        imageManager.refreshTempFileURL(clothingImageFileID, tempURL => {
          if (tempURL) {
            console.log('成功获取clothingImageFileID的临时URL:', tempURL);
            resolve(tempURL);
          } else {
            console.warn('获取clothingImageFileID的临时URL失败，尝试获取衣物数据');
            // 如果失败，尝试获取衣物数据
            getClothingData();
          }
        });
      } else {
        // 如果没有图片ID，尝试获取衣物数据
        console.log('没有有效的clothingImageFileID，尝试通过clothingId获取衣物数据');
        getClothingData();
      }

      // 获取衣物数据并提取图片URL
      function getClothingData() {
        if (!clothingId) {
          console.warn('没有衣物ID，无法获取衣物数据');
          resolve('/image/short-dress.png'); // 使用默认图片
          return;
        }

        outfitManager.getClothingById(clothingId)
          .then(clothingData => {
            if (!clothingData) {
              console.warn('获取衣物数据失败，使用默认图片');
              resolve('/image/short-dress.png');
              return;
            }

            // 优先使用抠图后的图片
            const imageFileID = clothingData.processedImageFileID || clothingData.imageFileID;

            if (imageFileID && imageFileID.includes('cloud://')) {
              imageManager.refreshTempFileURL(imageFileID, tempURL => {
                if (tempURL) {
                  console.log('成功获取衣物图片临时URL:', tempURL);
                  resolve(tempURL);
                } else {
                  console.warn('获取衣物图片临时URL失败，使用默认图片');
                  resolve('/image/short-dress.png');
                }
              });
            } else {
              console.warn('衣物没有有效的图片ID，使用默认图片');
              resolve('/image/short-dress.png');
            }
          })
          .catch(err => {
            console.error('获取衣物数据出错:', err);
            resolve('/image/short-dress.png');
          });
      }
    });
  },

  // 清除月份穿搭缓存
  clearMonthlyOutfitsCache: function() {
    const { userOpenId } = this.data;
    if (!userOpenId) return;

    const cacheKey = `${MONTHLY_OUTFITS_CACHE_KEY}${userOpenId}_${this.data.year}_${this.data.month}`;
    wx.removeStorageSync(cacheKey);
    console.log('已清除月份穿搭缓存:', cacheKey);
  },

  // 调试函数：手动查询特定日期的穿搭记录
  debugQuerySpecificDate: function(targetDate) {
    const { userOpenId } = this.data;
    if (!userOpenId) {
      console.error('用户OpenID不能为空');
      return;
    }

    console.log('开始调试查询特定日期的穿搭记录:', targetDate);

    const db = wx.cloud.database();
    const _ = db.command;

    // 创建目标日期的Date对象
    let queryDate;
    if (typeof targetDate === 'string') {
      queryDate = new Date(targetDate);
    } else {
      queryDate = targetDate;
    }

    // 创建查询范围：目标日期的00:00:00到23:59:59
    const startOfDay = new Date(queryDate);
    startOfDay.setHours(0, 0, 0, 0);

    const endOfDay = new Date(queryDate);
    endOfDay.setHours(23, 59, 59, 999);

    console.log('查询日期范围:', {
      开始: startOfDay,
      结束: endOfDay,
      目标日期: queryDate
    });

    // 查询该日期的穿搭记录
    db.collection('dailyOutfits')
      .where({
        _openid: userOpenId,
        date: _.gte(startOfDay).and(_.lte(endOfDay))
      })
      .get()
      .then(res => {
        console.log('调试查询结果:', res.data);

        if (res.data && res.data.length > 0) {
          res.data.forEach((item, index) => {
            console.log(`调试记录 ${index + 1}:`, {
              id: item._id,
              outfitId: item.outfitId,
              date: item.date,
              dateType: typeof item.date,
              outfitName: item.outfitName,
              createTime: item.createTime,
              updateTime: item.updateTime
            });
          });
        } else {
          console.log('该日期没有找到穿搭记录');
        }
      })
      .catch(err => {
        console.error('调试查询失败:', err);
      });
  },

  // 调试方法：查询5月26日的穿搭记录
  debugQueryMay26: function() {
    console.log('开始调试查询5月26日的穿搭记录');
    this.debugQuerySpecificDate('2025-05-26');
  },

  // 调试方法：清除缓存并刷新
  clearCacheAndRefresh: function() {
    console.log('清除缓存并刷新');

    // 清除月份穿搭缓存
    this.clearMonthlyOutfitsCache();

    // 清除所有相关缓存
    this.clearAllRelatedCaches();

    // 重新获取穿搭记录
    if (this.data.userOpenId) {
      this.getMonthlyOutfits();
    }

    wx.showToast({
      title: '缓存已清除，正在刷新',
      icon: 'success'
    });
  },

  // 格式化日期为YYYY-MM-DD
  getDateString: function(date) {
    return `${date.getFullYear()}-${date.getMonth() + 1}-${date.getDate()}`;
  },

  // 导航到添加穿搭页面
  navigateToAddOutfit: function() {
    const { selectedDate } = this.data;
    if (!selectedDate) return;

    // 直接使用选中的日期，无论是否为今天
    wx.navigateTo({
      url: `../ootd/ootd?date=${selectedDate}`
    });
  },

  // 返回上一页
  navigateBack: function() {
    wx.navigateBack();
  },

  // 查看穿搭详情
  viewOutfitDetail: function() {
    const { selectedDateOutfit } = this.data;
    if (!selectedDateOutfit) return;

    // 检查是否是多件衣物组合
    if (selectedDateOutfit.isMultipleClothings && selectedDateOutfit.clothingItems && selectedDateOutfit.clothingItems.length > 0) {
      // 显示多件衣物详情对话框
      this.showMultipleClothingDetail();
    }
    // 检查是否是单件衣物
    else if (selectedDateOutfit.isSingleClothing && selectedDateOutfit.singleClothingId) {
      // 跳转到衣物详情页面
      wx.navigateTo({
        url: `../closet/clothing_detail/clothing_detail?id=${selectedDateOutfit.singleClothingId}`
      });
    } else if (selectedDateOutfit.outfitId) {
      // 跳转到穿搭详情页面
      wx.navigateTo({
        url: `../outfit/outfit_detail/outfit_detail?id=${selectedDateOutfit.outfitId}`
      });
    }
  },

  // 显示多件衣物详情对话框
  showMultipleClothingDetail: function() {
    const { selectedDateOutfit } = this.data;
    if (!selectedDateOutfit || !selectedDateOutfit.clothingItems) return;

    // 构建详情信息
    const clothingList = selectedDateOutfit.clothingItems.map(item => {
      return `${item.name || '未命名衣物'} (${item.category || '未分类'})`;
    }).join('\n');

    const detailMessage = `组合穿搭包含 ${selectedDateOutfit.clothingItems.length} 件衣物：\n\n${clothingList}`;

    wx.showModal({
      title: '多件衣物详情',
      content: detailMessage,
      showCancel: true,
      cancelText: '关闭',
      confirmText: '查看单件',
      success: (res) => {
        if (res.confirm) {
          // 用户选择查看单件衣物，显示选择列表
          this.showClothingSelectionActionSheet();
        }
      }
    });
  },

  // 显示衣物选择列表
  showClothingSelectionActionSheet: function() {
    const { selectedDateOutfit } = this.data;
    if (!selectedDateOutfit || !selectedDateOutfit.clothingItems) return;

    const itemList = selectedDateOutfit.clothingItems.map(item => {
      return `${item.name || '未命名衣物'} (${item.category || '未分类'})`;
    });

    wx.showActionSheet({
      itemList: itemList,
      success: (res) => {
        const selectedIndex = res.tapIndex;
        const selectedClothing = selectedDateOutfit.clothingItems[selectedIndex];

        if (selectedClothing && selectedClothing.id) {
          // 跳转到选中的衣物详情页面
          wx.navigateTo({
            url: `../closet/clothing_detail/clothing_detail?id=${selectedClothing.id}`
          });
        }
      }
    });
  },

  // 预览穿搭图片
  previewOutfit: function() {
    const { selectedDateOutfit } = this.data;
    if (!selectedDateOutfit || !selectedDateOutfit.imageUrl) return;

    console.log('预览穿搭图片:', selectedDateOutfit.imageUrl);
    console.log('是否是单件衣物:', selectedDateOutfit.isSingleClothing);

    wx.previewImage({
      urls: [selectedDateOutfit.imageUrl],
      current: selectedDateOutfit.imageUrl
    });
  },

  // 预览单个衣物图片
  previewClothingImage: function(e) {
    const imageUrl = e.currentTarget.dataset.url;
    if (!imageUrl || imageUrl === '/image/short-dress.png') {
      wx.showToast({
        title: '暂无图片',
        icon: 'none'
      });
      return;
    }

    wx.previewImage({
      current: imageUrl,
      urls: [imageUrl]
    });
  },

  // 重新选择该天的穿搭
  selectAsToday: function() {
    const { selectedDate } = this.data;
    if (!selectedDate) {
      wx.showToast({
        title: '请先选择日期',
        icon: 'none'
      });
      return;
    }

    console.log('重新选择该天穿搭:', selectedDate);

    // 导航到OOTD页面选择新的穿搭
    wx.navigateTo({
      url: `../ootd/ootd?date=${selectedDate}&fromCalendar=true`
    });
  },

  // 创建新穿搭
  createOutfit: function() {
    wx.navigateTo({
      url: '../outfit/outfit_create/outfit_create'
    });
  },

  // 生成日历数据
  generateCalendarData: function() {
    console.log('生成日历数据');
    const { year, month } = this.data;

    // 获取当月第一天是周几
    const firstDay = new Date(year, month - 1, 1);
    const firstDayOfWeek = firstDay.getDay();

    // 获取当月天数
    const lastDay = new Date(year, month, 0);
    const daysInMonth = lastDay.getDate();

    // 获取当前日期
    const today = new Date();
    const currentDay = today.getDate();
    const currentMonth = today.getMonth() + 1;
    const currentYear = today.getFullYear();

    // 构建当月日期数组
    const days = [];
    for (let i = 1; i <= daysInMonth; i++) {
      const date = new Date(year, month - 1, i);
      days.push({
        day: i,
        date: this.getDateString(date),
        isToday: i === currentDay && month === currentMonth && year === currentYear,
        hasOutfit: false, // 默认没有穿搭记录
        isCurrentMonth: true
      });
    }

    this.setData({
      days: days,
      firstDayOfMonth: firstDayOfWeek
    }, () => {
      // 获取当月穿搭记录
      this.getMonthlyOutfits();
    });
  },

  // 加载指定日期的穿搭
  loadOutfitForDate: function(dateStr) {
    console.log('加载指定日期的穿搭:', dateStr);

    if (!dateStr) return;

    // 解析日期字符串
    const [year, month, day] = dateStr.split('-').map(Number);

    // 更新选中日期
    this.setData({
      selectedDay: day,
      selectedDate: dateStr,
      selectedDateFormatted: `${year}年${month}月${day}日`
    });

    // 查找该日期是否有穿搭记录
    const outfitData = this.data.monthlyOutfits[dateStr];
    console.log('加载指定日期的穿搭数据:', outfitData);

    if (outfitData) {
      // 检查是否是多件衣物组合
      if (outfitData.isMultipleClothings === true && outfitData.clothingIds && outfitData.clothingIds.length > 0) {
        console.log('检测到多件衣物组合作为穿搭:', outfitData.clothingIds);
        console.log('多件衣物详情:', outfitData.outfitItems);

        // 获取多件衣物的图片URLs
        this.getMultipleClothingImageUrls(outfitData.clothingIds, outfitData.outfitItems)
          .then(clothingItems => {
            console.log('获取到多件衣物图片URLs:', clothingItems);
            // 更新选中的穿搭数据
            this.setData({
              selectedDateOutfit: {
                ...outfitData,
                clothingItems: clothingItems,
                isMultipleClothings: true
              }
            });
          })
          .catch(err => {
            console.error('获取多件衣物图片URLs失败:', err);
            // 即使获取图片失败，也显示基本信息
            this.setData({
              selectedDateOutfit: {
                ...outfitData,
                isMultipleClothings: true,
                clothingItems: outfitData.outfitItems || []
              }
            });
          });
      }
      // 检查是否是单件衣物作为穿搭
      else if (outfitData.isSingleClothing === true && outfitData.singleClothingId) {
        console.log('检测到单件衣物作为穿搭:', outfitData.singleClothingId);
        console.log('单件衣物图片ID:', outfitData.clothingImageFileID);

        // 获取单件衣物图片URL
        this.getClothingImageUrl(outfitData.singleClothingId, outfitData.clothingImageFileID)
          .then(imageUrl => {
            console.log('获取到单件衣物图片URL:', imageUrl);
            // 更新选中的穿搭数据
            this.setData({
              selectedDateOutfit: {
                ...outfitData,
                imageUrl: imageUrl,
                isSingleClothing: true
              }
            });
          })
          .catch(err => {
            console.error('获取单件衣物图片URL失败:', err);
            // 即使获取图片失败，也显示基本信息
            this.setData({
              selectedDateOutfit: {
                ...outfitData,
                isSingleClothing: true
              }
            });
          });
      } else {
        // 常规穿搭记录，获取详细信息
        const outfitId = outfitData.outfitId;
        const outfitDetail = this.data.outfitsCache[outfitId];
        console.log('常规穿搭记录:', outfitId, '详情:', outfitDetail);

        if (outfitDetail) {
          // 获取图片临时URL
          this.getOutfitImageUrl(outfitDetail)
            .then(imageUrl => {
              // 更新选中的穿搭数据
              this.setData({
                selectedDateOutfit: {
                  ...outfitData,
                  imageUrl: imageUrl,
                  outfit: outfitDetail,
                  isSingleClothing: false
                }
              });
            })
            .catch(err => {
              console.error('获取穿搭图片URL失败:', err);

              // 即使获取图片失败，也显示穿搭信息
              this.setData({
                selectedDateOutfit: {
                  ...outfitData,
                  outfit: outfitDetail,
                  isSingleClothing: false
                }
              });
            });
        } else {
          // 找不到穿搭详情，只显示基本信息
          console.log('找不到穿搭详情，只显示基本信息');
          this.setData({
            selectedDateOutfit: {
              ...outfitData,
              isSingleClothing: false
            }
          });
        }
      }
    } else {
      // 没有穿搭记录，清空选中状态
      console.log('没有穿搭记录，清空选中状态');
      this.setData({
        selectedDateOutfit: null
      });
    }
  },

  // 刷新日历UI
  refreshCalendarUI: function() {
    console.log('刷新日历UI');

    // 先获取今天的穿搭记录，然后再刷新日历
    const today = new Date();
    const todayString = `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`;

    // 检查今天是否有穿搭（使用修复后的getTodayOutfit函数）
    outfitManager.getTodayOutfit(this.data.userOpenId)
      .then(todayOutfit => {
        console.log('获取今天穿搭记录结果:', todayOutfit);

        // 判断是否有穿搭记录
        const hasOutfit = todayOutfit !== null;
        let todayOutfitId = null;

        if (hasOutfit) {
          console.log('今天有穿搭:', todayOutfit);
          todayOutfitId = todayOutfit.outfitId;
          console.log('今天的穿搭 ID:', todayOutfitId);

          // 打印日期信息以确认这确实是今天的记录
          const outfitDate = new Date(todayOutfit.date);
          console.log('穿搭记录的日期:', outfitDate, '今天日期:', today);
        } else {
          console.log('今天没有穿搭记录');
        }

        // 重新生成日历数据
        this.generateCalendarData();

        // 如果有选中的日期，重新加载该日期的穿搭
        if (this.data.selectedDate) {
          this.loadOutfitForDate(this.data.selectedDate);
        }

        // 手动更新今天的标记
        if (hasOutfit && todayOutfitId) {
          // 更新今天的标记
          const days = this.data.days;

          // 找到今天在日历中的位置
          const todayIndex = days.findIndex(day => {
            return day.isToday;
          });

          console.log('今天在日历中的位置:', todayIndex);

          if (todayIndex !== -1) {
            // 更新今天的标记
            days[todayIndex].hasOutfit = true;
            days[todayIndex].outfitId = todayOutfitId;

            console.log('手动更新今天的标记为有穿搭，穿搭 ID:', todayOutfitId);

            // 如果今天已经被选中，也更新选中的穿搭数据
            if (this.data.selectedDate === todayString && this.data.selectedDateOutfit) {
              console.log('今天已经被选中，更新选中的穿搭数据');
              this.setData({
                'selectedDateOutfit.outfitId': todayOutfitId
              });
            }

            this.setData({
              days: days
            });
          }
        }
      })
      .catch(err => {
        console.error('获取今天穿搭失败:', err);

        // 即使获取失败，也继续刷新日历
        this.generateCalendarData();

        // 如果有选中的日期，重新加载该日期的穿搭
        if (this.data.selectedDate) {
          this.loadOutfitForDate(this.data.selectedDate);
        }
      });
  },

  // 复制为今日穿搭
  copyToToday: function() {
    const { selectedDateOutfit, userOpenId } = this.data;
    if (!selectedDateOutfit || !userOpenId) {
      console.error('缺少必要参数:', {
        hasOutfit: !!selectedDateOutfit,
        outfitId: selectedDateOutfit ? selectedDateOutfit.outfitId : null,
        userOpenId
      });

      wx.showToast({
        title: '请先选择穿搭',
        icon: 'none'
      });
      return;
    }

    // 确保我们有outfitId
    if (!selectedDateOutfit.outfitId) {
      console.error('缺少穿搭ID');
      wx.showToast({
        title: '无效的穿搭数据',
        icon: 'none'
      });
      return;
    }

    // 检查是否是单件衣物
    console.log('复制为今日穿搭，是否是单件衣物:', selectedDateOutfit.isSingleClothing);

    // 获取今天的日期字符串
    const today = new Date();
    const todayString = `${today.getFullYear()}-${today.getMonth() + 1}-${today.getDate()}`;

    console.log('开始复制穿搭到今日:', {
      outfitId: selectedDateOutfit.outfitId,
      todayString,
      userOpenId,
      selectedDateOutfit: JSON.stringify(selectedDateOutfit)
    });

    // 再次确认outfitManager模块中包含所需函数
    const hasGetCompleteOutfitData = typeof outfitManager.getCompleteOutfitData === 'function';
    const hasSaveOutfitToDate = typeof outfitManager.saveOutfitToDate === 'function';

    console.log('outfitManager函数检查:', {
      hasGetCompleteOutfitData,
      hasSaveOutfitToDate,
      hasSaveTodayOutfit: typeof outfitManager.saveTodayOutfit === 'function'
    });

    // 检查必要的函数是否可用
    if (!hasGetCompleteOutfitData || !hasSaveOutfitToDate) {
      console.error('缺少必要的函数:', {
        hasGetCompleteOutfitData,
        hasSaveOutfitToDate
      });

      wx.showToast({
        title: '系统错误，请重启应用',
        icon: 'none'
      });
      return;
    }

    wx.showLoading({ title: '正在保存...' });

    // 首先检查今天是否已有穿搭记录
    const checkAndSaveOutfit = (outfitData) => {
      // 确保我们有完整的outfitData
      if (!outfitData || (!outfitData._id && !outfitData.id)) {
        console.error('穿搭数据不完整:', outfitData);
        wx.hideLoading();
        wx.showToast({
          title: '无法保存，穿搭数据不完整',
          icon: 'none'
        });
        return;
      }

      // 检查是否是单件衣物
      const isSingleClothing = selectedDateOutfit.isSingleClothing;
      const singleClothingId = selectedDateOutfit.singleClothingId;
      const clothingImageFileID = selectedDateOutfit.clothingImageFileID;

      console.log('准备保存穿搭数据:', JSON.stringify({
        ...outfitData,
        isSingleClothing,
        singleClothingId,
        clothingImageFileID
      }));

      // 先获取今天的穿搭记录
      console.log('检查今天是否已有穿搭记录');
      outfitManager.getTodayOutfit(userOpenId)
        .then(todayOutfit => {
          // 如果已有记录，使用其ID进行更新
          const existingRecordId = todayOutfit ? todayOutfit._id : null;
          console.log('今天的穿搭记录状态:', {
            hasExistingRecord: !!existingRecordId,
            existingRecordId: existingRecordId
          });

          // 直接使用saveOutfitToDate函数，传入today作为目标日期
          console.log('调用saveOutfitToDate函数保存到今天，现有记录ID:', existingRecordId);

          // 如果是单件衣物，添加相关信息
          if (selectedDateOutfit.isSingleClothing) {
            outfitData.isSingleClothing = true;
            outfitData.singleClothingId = selectedDateOutfit.singleClothingId;
            outfitData.clothingImageFileID = selectedDateOutfit.clothingImageFileID;
            outfitData.outfitName = selectedDateOutfit.outfitName || '单件衣物';
            outfitData.outfitType = selectedDateOutfit.outfitType || 'daily';
          }

          return outfitManager.saveOutfitToDate(outfitData, userOpenId, todayString, existingRecordId);
        })
        .then(result => {
          console.log('保存今日穿搭成功:', result);

          // 保存成功后，清除所有相关缓存
          this.clearAllRelatedCaches();

          wx.hideLoading();
          wx.showToast({
            title: '已复制为今日穿搭',
            icon: 'success'
          });

          // 设置标记，通知页面刷新
          wx.setStorageSync('needRefreshOutfits', true);

          // 直接更新今天的标记，如果当前月份包含今天的话
          const days = this.data.days;
          const todayIndex = days.findIndex(day => day.isToday);

          if (todayIndex !== -1) {
            // 更新今天的标记
            days[todayIndex].hasOutfit = true;
            days[todayIndex].outfitId = outfitData._id || outfitData.id;

            console.log('直接更新今天的标记为有穿搭，穿搭 ID:', outfitData._id || outfitData.id);

            // 保存穿搭数据到本地缓存，确保有最新的图片URL
            const outfitId = outfitData._id || outfitData.id;

            // 预先处理穿搭图片URL
            this.getOutfitImageUrl(outfitData)
              .then(imageUrl => {
                console.log('获取到最新的穿搭图片URL:', imageUrl);

                // 更新缓存中的穿搭数据
                const newOutfitsCache = {...this.data.outfitsCache};
                newOutfitsCache[outfitId] = outfitData;

                // 更新穿搭数据
                const updatedOutfit = {
                  ...outfitData,
                  previewImage: imageUrl  // 确保有预览图URL
                };
                newOutfitsCache[outfitId] = updatedOutfit;

                this.setData({
                  days: days,
                  outfitsCache: newOutfitsCache
                }, () => {
                  // 判断是否今天被选中
                  if (this.data.selectedDate === todayString) {
                    // 更新选中的穿搭数据，使用最新的图片URL
                    this.setData({
                      selectedDateOutfit: {
                        outfitId: outfitId,
                        outfitName: outfitData.name || '穿搭',
                        outfitType: outfitData.type || outfitData.category || 'daily',
                        imageUrl: imageUrl,
                        outfit: updatedOutfit
                      }
                    });
                  }

                  // 延迟一下再刷新日历，确保数据已经保存到云端
                  setTimeout(() => {
                    // 刷新日历UI
                    this.refreshCalendarUI();

                    // 重新加载数据
                    this.getMonthlyOutfits();
                  }, 500);
                });
              })
              .catch(err => {
                console.error('获取穿搭图片URL失败:', err);
                this.setData({
                  days: days
                });

                // 延迟一下再刷新日历，确保数据已经保存到云端
                setTimeout(() => {
                  // 刷新日历UI
                  this.refreshCalendarUI();

                  // 重新加载数据
                  this.getMonthlyOutfits();
                }, 500);
              });
          } else {
            console.log('当前日历不包含今天，无法直接更新UI');

            // 向用户展示操作成功的提示，并询问是否跳转到今天所在月份
            wx.showModal({
              title: '复制成功',
              content: '已将该穿搭设置为今日穿搭。今天不在当前月份的日历中，是否切换到今天所在的月份查看？',
              confirmText: '去查看',
              cancelText: '留在当前',
              success: (res) => {
                if (res.confirm) {
                  // 用户点击了确认，跳转到今天所在月份
                  console.log('用户选择跳转到今天所在月份');

                  // 先确保完整的穿搭数据和图片URL被缓存
                  const outfitId = outfitData._id || outfitData.id;

                  // 预先处理穿搭图片URL
                  this.getOutfitImageUrl(outfitData)
                    .then(imageUrl => {
                      console.log('获取到最新的穿搭图片URL(跳转模式):', imageUrl);

                      // 更新穿搭数据
                      const updatedOutfit = {
                        ...outfitData,
                        previewImage: imageUrl  // 确保有预览图URL
                      };

                      const tempOutfitsCache = {};
                      tempOutfitsCache[outfitId] = updatedOutfit;

                      // 保存到全局数据中，以便jumpToToday后能够使用
                      this.setData({
                        outfitsCache: {
                          ...this.data.outfitsCache,
                          ...tempOutfitsCache
                        },
                        // 设置一个标记，表明我们刚刚复制了一个穿搭到今天
                        justCopiedOutfit: true,
                        justCopiedOutfitId: outfitId,
                        // 保存最新的图片URL
                        justCopiedImageUrl: imageUrl
                      }, () => {
                        // 跳转到今天所在月份
                        this.jumpToToday();
                      });
                    })
                    .catch(err => {
                      console.error('获取穿搭图片URL失败(跳转模式):', err);

                      // 即使获取图片失败，也进行跳转
                      const tempOutfitsCache = {};
                      tempOutfitsCache[outfitId] = outfitData;

                      this.setData({
                        outfitsCache: {
                          ...this.data.outfitsCache,
                          ...tempOutfitsCache
                        },
                        justCopiedOutfit: true,
                        justCopiedOutfitId: outfitId
                      }, () => {
                        this.jumpToToday();
                      });
                    });
                } else {
                  // 用户选择留在当前月份，只提示用户操作成功
                  console.log('用户选择留在当前月份');
                  wx.showToast({
                    title: '已设置为今日穿搭',
                    icon: 'success',
                    duration: 2000
                  });
                }
              }
            });
          }
        })
        .catch(err => {
          console.error('保存今日穿搭失败:', err);

          wx.hideLoading();
          wx.showToast({
            title: '保存失败，请重试',
            icon: 'none'
          });
        });
    };

    // 尝试获取完整的穿搭数据
    console.log('开始获取完整穿搭数据:', selectedDateOutfit.outfitId);
    outfitManager.getCompleteOutfitData(selectedDateOutfit.outfitId)
      .then(outfitData => {
        console.log('获取到完整穿搭数据:', outfitData);

        // 使用我们的新函数保存到今天
        console.log('获取到完整穿搭数据，调用checkAndSaveOutfit');
        checkAndSaveOutfit(outfitData);
      })
      .catch(err => {
        console.error('获取完整穿搭数据失败:', err);

        // 如果获取完整数据失败，尝试直接使用选中的穿搭数据
        console.log('尝试使用选中的穿搭数据:', selectedDateOutfit);
        if (selectedDateOutfit && selectedDateOutfit.outfitId) {
          // 确保有最基本的数据
          if (!selectedDateOutfit.name) {
            selectedDateOutfit.name = '穿搭';
          }
          if (!selectedDateOutfit.type && !selectedDateOutfit.category) {
            selectedDateOutfit.type = 'daily';
          }

          checkAndSaveOutfit(selectedDateOutfit);
        } else {
          wx.hideLoading();
          wx.showToast({
            title: '无法获取穿搭数据',
            icon: 'none'
          });
        }
      });
  },

  // 清除所有相关缓存
  clearAllRelatedCaches: function() {
    console.log('清除所有相关缓存');

    // 清除当前月份的穿搭缓存
    this.clearMonthlyOutfitsCache();

    // 清除今天所在月份的穿搭缓存（如果不是当前月份）
    const today = new Date();
    const currentYear = today.getFullYear();
    const currentMonth = today.getMonth() + 1;

    if (this.data.year !== currentYear || this.data.month !== currentMonth) {
      console.log('清除今天所在月份的缓存');
      const { userOpenId } = this.data;
      if (userOpenId) {
        const todayCacheKey = `${MONTHLY_OUTFITS_CACHE_KEY}${userOpenId}_${currentYear}_${currentMonth}`;
        wx.removeStorageSync(todayCacheKey);
      }
    }

    // 设置穿搭数据需要刷新的标志
    wx.setStorageSync('needRefreshOutfits', true);
  },
})