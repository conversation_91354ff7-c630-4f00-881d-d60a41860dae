<view class="page {{themeStyle === 'autumn' ? 'page-autumn' : (themeStyle === 'pinkBlue' ? 'page-pinkBlue' : 'page-blackWhite')}}" data-weui-theme="{{theme}}" style="background-color: {{themeStyle === 'autumn' ? pageStyle.backgroundColor : (themeStyle === 'pinkBlue' ? pageStyle.backgroundColor : '#dee3ec')}}; background-image: {{pageStyle.backgroundImage}};">
  <!-- 顶部自定义导航栏 -->
  <view class="top-section">
    <view class="title-container">
      <view class="section-title" style="color: {{themeStyle === 'autumn' ? colors.cowhide_cocoa : (themeStyle === 'pinkBlue' ? pinkBlueColors.pinkDark : blackWhiteColors.black)}};">个人设置</view>

    </view>
  </view>

  <!-- 添加wxs模块，用于辅助判断 -->
  <wxs module="helper">
    // 检查数组中是否包含某个值的辅助函数
    function containsStep(array, step) {
      if (!array) {
        return false;
      }

      for (var i = 0; i < array.length; i++) {
        if (array[i] == step) {
          return true;
        }
      }
      return false;
    }

    module.exports = {
      containsStep: containsStep
    };
  </wxs>

  <!-- 装饰元素 -->
  <view class="decoration-element decoration-1" style="background-color: {{themeStyle === 'autumn' ? colors.olive_harvest : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueMedium : blackWhiteColors.black)}};"></view>
  <view class="decoration-element decoration-2" style="background-color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.pinkMedium : blackWhiteColors.mediumGray)}};"></view>
  <view class="decoration-element decoration-3" style="background-color: {{themeStyle === 'autumn' ? colors.toasted_caramel : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueLight : blackWhiteColors.lightGray)}};"></view>

  <!-- 用户信息卡片 -->
  <view class="user-info-card" style="background-color: {{themeStyle === 'autumn' ? 'rgba(255, 255, 255, 0.8)' : (themeStyle === 'pinkBlue' ? 'rgba(255, 255, 255, 0.9)' : 'white')}}; color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueDark : blackWhiteColors.black)}}; {{themeStyle === 'blackWhite' ? 'border: 1px solid ' + blackWhiteColors.black + ';' : ''}}">
    <view class="user-avatar-container" bindtap="showEnergyRules">
      <!-- 猫咪体力值圆环进度条 -->
      <view class="avatar-energy-ring" style="background: conic-gradient({{themeStyle === 'autumn' ? colors.toasted_caramel : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueMedium : blackWhiteColors.black)}} {{userInfo.catEnergy / 500 * 360}}deg, transparent 0deg);"></view>
      <!-- 用户头像/猫咪图片 -->
      <image class="user-avatar" src="{{catImages.isLoaded ? (userInfo.catEnergy < 50 ? catImages.tireCat : (userInfo.catEnergy <= 200 ? catImages.normalCat : catImages.happyCat)) : userInfo.avatarUrl}}" mode="aspectFill" data-type="{{catImages.isLoaded ? (userInfo.catEnergy < 50 ? 'tireCat' : (userInfo.catEnergy <= 200 ? 'normalCat' : 'happyCat')) : ''}}" binderror="onCatAvatarError"></image>
      <!-- 体力值数字显示 -->
      <view class="avatar-energy-value">{{userInfo.catEnergy}}</view>
      <!-- 查看体力值提示 -->
      <view class="avatar-tip">点击查看猫咪体力值</view>
    </view>
    <view class="user-info-details">
      <!-- 左侧区域：称号和体力值 -->
      <view class="user-info-left">
        <!-- 称号容器 -->
        <view class="user-title-container" bindtap="toggleTitleSelector">
          <view class="user-title" style="color: {{themeStyle === 'autumn' ? colors.olive_harvest : (themeStyle === 'pinkBlue' ? pinkBlueColors.pinkDark : blackWhiteColors.black)}};">{{userInfo.title}}</view>
          <view class="title-dropdown-icon {{showTitleSelector ? 'title-dropdown-open' : ''}}" style="color: {{themeStyle === 'autumn' ? colors.olive_harvest : (themeStyle === 'pinkBlue' ? pinkBlueColors.pinkDark : blackWhiteColors.black)}};">▼</view>
        </view>

        <!-- UID显示 - 称号下方 -->
        <view class="user-uid" bindtap="copyUserOpenid" style="color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueMedium : blackWhiteColors.darkGray)}}; opacity: 0.75;">
          UID: {{uidDisplay || '加载中...'}} <text class="copy-icon" style="margin-left: 4rpx; font-size: 20rpx;"></text>
        </view>

        <!-- 称号选择器下拉菜单 -->
        <view class="title-selector {{showTitleSelector ? 'title-selector-show' : ''}}" style="background-color: {{themeStyle === 'autumn' ? 'rgba(255, 255, 255, 0.95)' : (themeStyle === 'pinkBlue' ? 'rgba(255, 255, 255, 0.98)' : 'white')}}; border-color: {{themeStyle === 'autumn' ? colors.olive_harvest : (themeStyle === 'pinkBlue' ? pinkBlueColors.pinkLight : blackWhiteColors.black)}}">
          <block wx:for="{{availableTitles}}" wx:key="index">
            <view class="title-option {{userInfo.title === item ? 'title-option-selected' : ''}}"
                  bindtap="selectUserTitle"
                  data-title="{{item}}"
                  style="{{userInfo.title === item ? (themeStyle === 'autumn' ? 'background-color: rgba(157, 145, 103, 0.2);' : (themeStyle === 'pinkBlue' ? 'background-color: rgba(249, 201, 214, 0.2);' : 'background-color: rgba(0, 0, 0, 0.1);')) : ''}}">
              <text>{{item}}</text>
              <text wx:if="{{userInfo.title === item}}" class="title-selected-icon" style="color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.pinkDark : blackWhiteColors.black)}};">✓</text>
            </view>
          </block>
        </view>


      </view>

      <!-- 右侧区域：会员信息 -->
      <view class="user-info-right">
        <view class="member-info-container" bindtap="showMemberBenefits">
          <!-- 会员信息卡片 -->
          <view class="member-card {{userInfo.memberType === 'VIP' ? 'vip-member-card' : 'normal-member-card'}}">
            <view class="member-card-header">
              <text wx:if="{{userInfo.memberType === 'VIP'}}" class="vip-icon">★</text>
              <text class="member-type-text">{{userInfo.memberType === 'VIP' ? 'VIP' : '普通会员'}}</text>
            </view>
            <view class="member-card-body" wx:if="{{userInfo.memberType === 'VIP' && userInfo.memberDaysLeft > 0}}">
              <text class="member-days-left">{{userInfo.memberDaysLeft}}天</text>
            </view>
          </view>
          <!-- 查看会员权益提示 -->
          <view class="member-tip">👑 查看会员权益</view>
        </view>



      </view>
    </view>
  </view>

  <!-- 主题任务进度卡片 -->
  <view class="task-progress-card" style="background-color: {{themeStyle === 'autumn' ? 'rgba(255, 255, 255, 0.8)' : (themeStyle === 'pinkBlue' ? 'rgba(255, 255, 255, 0.9)' : 'white')}}; color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueDark : blackWhiteColors.black)}}; {{themeStyle === 'blackWhite' ? 'border: 1px solid ' + blackWhiteColors.black + ';' : ''}}">
    <view class="task-title" style="color: {{themeStyle === 'autumn' ? colors.cowhide_cocoa : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueDark : blackWhiteColors.black)}};">
      <text class="task-icon">🌟</text>
      <text>抓猫猫</text>
      <text class="task-subtitle" style="font-size: 24rpx; opacity: 0.8; display: block; margin-top: 4rpx;">（完成每日任务即可抽取猫咪卡片）</text>
      <!-- 添加刷新按钮 -->
      <view class="refresh-btn" bindtap="refreshTaskProgress" style="color: {{themeStyle === 'autumn' ? colors.olive_harvest : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueMedium : blackWhiteColors.black)}};">
        <text class="refresh-icon">🔄</text>
      </view>
    </view>

    <!-- 任务描述 -->
    <view class="task-description" style="color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.pinkDark : blackWhiteColors.black)}};">
      {{ themeTasks.taskDescriptions[themeTasks.currentTask - 1] || themeTasks.taskDescriptions[0] }}
    </view>

    <!-- 任务进度条 -->
    <view class="task-progress-bar">
      <view class="task-progress-inner" style="width: {{(themeTasks.currentStep / 6) * 100}}%; background-color: {{themeStyle === 'autumn' ? colors.olive_harvest : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueMedium : blackWhiteColors.black)}};"></view>
    </view>
    <view class="task-progress-text">
      {{themeTasks.currentStep}}/6
    </view>

    <!-- 任务步骤图片展示 -->
    <view class="task-steps-container">
      <!-- 加载中提示 -->
      <view wx:if="{{imageLoading.isLoading}}" class="task-loading-overlay">
        <view class="task-loading-content">
          <view class="task-loading-spinner"></view>
          <view class="task-loading-text">加载图片 ({{imageLoading.loadedCount}}/{{imageLoading.totalCount}})</view>
        </view>
      </view>

      <block wx:for="{{6}}" wx:key="index">
        <view class="task-step-item {{helper.containsStep(themeTasks.hasDone, index + 1) ? 'task-step-completed' : 'task-step-incomplete'}}"
              bindtap="showTaskStepDescription"
              data-index="{{index}}">
          <image
            class="task-step-image"
            src="{{themeTasks.taskImages[index]}}"
            mode="aspectFill"
            style="opacity: {{helper.containsStep(themeTasks.hasDone, index + 1) ? 1 : 0.5}};"
            data-index="{{index}}"
            binderror="onTaskStepImageError"
          ></image>
          <view class="task-step-number" style="background-color: {{helper.containsStep(themeTasks.hasDone, index + 1) ? (themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.pinkDark : blackWhiteColors.black)) : '#cccccc'}};">{{index + 1}}</view>
          <!-- 添加标题显示 -->
          <view class="task-step-title" style="color: {{themeStyle === 'autumn' ? colors.cowhide_cocoa : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueDark : blackWhiteColors.black)}};">
            {{themeTasks.titles[index] || '步骤' + (index + 1)}}
          </view>
        </view>
      </block>
    </view>
  </view>

  <!-- 横向滑动提示 -->
  <view class="scroll-hint" style="text-align: center; padding: 16rpx 0; font-size: 26rpx; color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueDark : blackWhiteColors.black)}}; opacity: 0.8;">
    <text>👇 向右滑动查看更多功能 👇</text>
  </view>

  <!-- 底部导航栏 - 现在放在底部工具栏上方 -->
  <view class="bottom-nav-bar card-nav-bar">
    <!-- 每日签到卡片 -->
    <view class="nav-card" bindtap="{{checkInData.isLoading ? '' : 'doCheckIn'}}" bindlongpress="debugCheckInStatus" style="background-color: {{themeStyle === 'autumn' ? 'rgba(255, 255, 255, 0.7)' : (themeStyle === 'pinkBlue' ? 'rgba(255, 255, 255, 0.8)' : 'white')}}; color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueDark : blackWhiteColors.black)}}; {{themeStyle === 'blackWhite' ? 'border: 1px solid ' + blackWhiteColors.black + ';' : ''}}; {{checkInData.isLoading ? 'opacity: 0.8;' : ''}}">"
      <view class="nav-card-content">
        <!-- 连续签到天数标签 -->
        <view class="checkin-days-badge" style="background-color: {{themeStyle === 'autumn' ? colors.toasted_caramel : (themeStyle === 'pinkBlue' ? pinkBlueColors.pinkMedium : blackWhiteColors.black)}};">
          <text>连续{{checkInData.consecutiveDays}}天</text>
        </view>

        <text class="nav-text" style="margin-top: 20rpx;">每日签到</text>


        <!-- 签到按钮，放在底部 -->
        <view class="checkin-status" style="{{checkInData.todayCheckedIn ? 'margin-top: 30rpx;' : 'margin-top: 10rpx;'}}">
          <view class="checkin-button {{checkInData.todayCheckedIn ? 'checked' : ''}} {{checkInData.isLoading ? 'loading' : ''}}" style="width: {{checkInData.todayCheckedIn ? '70rpx' : (checkInData.isLoading ? '100rpx' : '60rpx')}}; text-align: center; {{checkInData.todayCheckedIn ? 'background-color: ' + (themeStyle === 'autumn' ? '#6FA366' : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueMedium : '#555')) + ';' : 'background-color: ' + (themeStyle === 'autumn' ? colors.toasted_caramel : (themeStyle === 'pinkBlue' ? pinkBlueColors.pinkMedium : blackWhiteColors.black)) + ';'}}">
            <text wx:if="{{!checkInData.isLoading}}">{{checkInData.todayCheckedIn ? '已签到' : '签到'}}</text>
            <text wx:else>加载中...</text>
          </view>
        </view>
      </view>
    </view>

    <!-- 每日任务卡片 -->
    <view class="nav-card" bindtap="showDailyTasksCard" style="background-color: {{themeStyle === 'autumn' ? 'rgba(255, 255, 255, 0.7)' : (themeStyle === 'pinkBlue' ? 'rgba(255, 255, 255, 0.8)' : 'white')}}; color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueDark : blackWhiteColors.black)}}; {{themeStyle === 'blackWhite' ? 'border: 1px solid ' + blackWhiteColors.black + ';' : ''}}">
      <view class="nav-card-content">
        <text class="nav-icon">📝</text>
        <text class="nav-text">每日任务</text>
        <view class="daily-tasks-mini-progress">
          <text class="daily-tasks-mini-count">{{dailyTasks.completedCount}}/{{dailyTasks.totalCount}}</text>
          <view class="daily-tasks-mini-bar">
            <view class="daily-tasks-mini-inner" style="width: {{(dailyTasks.completedCount / dailyTasks.totalCount) * 100}}%; background-color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.pinkDark : blackWhiteColors.black)}};"></view>
          </view>
        </view>
      </view>
    </view>

    <!-- 看广告得体力卡片 -->
    <view class="nav-card" bindtap="showVideoAd" style="background-color: {{themeStyle === 'autumn' ? 'rgba(255, 255, 255, 0.7)' : (themeStyle === 'pinkBlue' ? 'rgba(255, 255, 255, 0.8)' : 'white')}}; color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueDark : blackWhiteColors.black)}}; {{themeStyle === 'blackWhite' ? 'border: 1px solid ' + blackWhiteColors.black + ';' : ''}}">
      <view class="nav-card-content">
        <text class="nav-icon">📺</text>
        <text class="nav-text">看广告得体力</text>
      </view>
    </view>

        <!-- 使用限制卡片 -->
    <view class="nav-card" style="background-color: {{themeStyle === 'autumn' ? 'rgba(255, 255, 255, 0.7)' : (themeStyle === 'pinkBlue' ? 'rgba(255, 255, 255, 0.8)' : 'white')}}; color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueDark : blackWhiteColors.black)}}; {{themeStyle === 'blackWhite' ? 'border: 1px solid ' + blackWhiteColors.black + ';' : ''}}">
      <view class="nav-card-content">
        <text class="nav-icon">📊</text>
        <text class="nav-text">使用限制</text>
        <view wx:if="{{limitLoaded}}" class="limit-mini-display">
          <view class="limit-mini-item">
            <text>服装: {{userLimits.clothesCount}}/{{userLimits.clothesLimit}}</text>
          </view>
          <view class="limit-mini-item">
            <text>穿搭: {{userLimits.outfitsCount}}/{{userInfo.memberType === 'VIP' ? '无限制' : userLimits.outfitsLimit}}</text>
          </view>
        </view>
        <view wx:else class="limit-mini-display">
          <text>数据加载中...</text>
        </view>
      </view>
    </view>

        <!-- 消息通知卡片 -->
    <view class="nav-card" bindtap="viewMessages" style="background-color: {{themeStyle === 'autumn' ? 'rgba(255, 255, 255, 0.7)' : (themeStyle === 'pinkBlue' ? 'rgba(255, 255, 255, 0.8)' : 'white')}}; color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueDark : blackWhiteColors.black)}}; {{themeStyle === 'blackWhite' ? 'border: 1px solid ' + blackWhiteColors.black + ';' : ''}}">
      <view class="nav-card-content">
        <view class="nav-icon-with-badge">
          <text class="nav-icon">📩</text>
          <view wx:if="{{unreadCount > 0}}" class="message-badge" style="background-color: {{themeStyle === 'autumn' ? colors.toasted_caramel : (themeStyle === 'pinkBlue' ? pinkBlueColors.pinkMedium : blackWhiteColors.black)}};">
            {{unreadCount > 99 ? '99+' : unreadCount}}
          </view>
        </view>
        <text class="nav-text">消息</text>
      </view>
    </view>

    <!-- 外观风格卡片 -->
    <view class="nav-card" style="background-color: {{themeStyle === 'autumn' ? 'rgba(255, 255, 255, 0.7)' : (themeStyle === 'pinkBlue' ? 'rgba(255, 255, 255, 0.8)' : 'white')}}; color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueDark : blackWhiteColors.black)}}; {{themeStyle === 'blackWhite' ? 'border: 1px solid ' + blackWhiteColors.black + ';' : ''}}">
      <view class="nav-card-content">
        <text class="nav-icon">🎨</text>
        <text class="nav-text">外观风格</text>
        <view class="theme-select-button" bindtap="showThemeSelector">
          <view class="current-theme-preview">
            <block wx:if="{{themeStyle === 'autumn'}}">
              <view class="theme-preview-color" style="background-color: {{colors.golden_batter}}"></view>
              <view class="theme-preview-color" style="background-color: {{colors.spiced_wine}}"></view>
              <view class="theme-preview-color" style="background-color: {{colors.olive_harvest}}"></view>
            </block>
            <block wx:elif="{{themeStyle === 'pinkBlue'}}">
              <view class="theme-preview-color" style="background-color: {{pinkBlueColors.pinkLight}}"></view>
              <view class="theme-preview-color" style="background-color: {{pinkBlueColors.pinkDark}}"></view>
              <view class="theme-preview-color" style="background-color: {{pinkBlueColors.blueMedium}}"></view>
            </block>
            <block wx:elif="{{themeStyle === 'blackWhite'}}">
              <view class="theme-preview-color" style="background-color: {{blackWhiteColors.black}}"></view>
              <view class="theme-preview-color" style="background-color: {{blackWhiteColors.mediumGray}}"></view>
              <view class="theme-preview-color" style="background-color: {{blackWhiteColors.white}}"></view>
            </block>
          </view>
          <view class="theme-select-arrow">▼</view>
        </view>
      </view>
    </view>



    <!-- 兑换码卡片 -->
    <navigator url="/page/settings/redeem/redeem" class="nav-card" hover-class="nav-card-active" style="background-color: {{themeStyle === 'autumn' ? 'rgba(255, 255, 255, 0.7)' : (themeStyle === 'pinkBlue' ? 'rgba(255, 255, 255, 0.8)' : 'white')}}; color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueDark : blackWhiteColors.black)}}; {{themeStyle === 'blackWhite' ? 'border: 1px solid ' + blackWhiteColors.black + ';' : ''}}">
      <view class="nav-card-content">
        <text class="nav-icon">🎁</text>
        <text class="nav-text">兑换码</text>
        <text class="nav-subtitle">点击使用</text>
      </view>
    </navigator>

    <!-- 换季箱卡片 -->
    <view class="nav-card vip-feature" bindtap="viewStorageBox" style="background-color: {{themeStyle === 'autumn' ? 'rgba(255, 255, 255, 0.7)' : (themeStyle === 'pinkBlue' ? 'rgba(255, 255, 255, 0.8)' : 'white')}}; color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueDark : blackWhiteColors.black)}}; {{themeStyle === 'blackWhite' ? 'border: 1px solid ' + blackWhiteColors.black + ';' : ''}}">
      <view class="vip-badge" style="{{themeStyle === 'blackWhite' ? 'background-color: ' + blackWhiteColors.black + '; color: white;' : ''}}">VIP</view>
      <view class="nav-card-content">
        <text class="nav-icon">📦</text>
        <text class="nav-text">换季箱</text>
        <text class="nav-subtitle" wx:if="{{storageCount !== undefined}}">{{storageCount}}件</text>
      </view>
    </view>

    <!-- 断舍离箱卡片 -->
    <view class="nav-card vip-feature" bindtap="viewDiscardBox" style="background-color: {{themeStyle === 'autumn' ? 'rgba(255, 255, 255, 0.7)' : (themeStyle === 'pinkBlue' ? 'rgba(255, 255, 255, 0.8)' : 'white')}}; color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueDark : blackWhiteColors.black)}}; {{themeStyle === 'blackWhite' ? 'border: 1px solid ' + blackWhiteColors.black + ';' : ''}}">
      <view class="vip-badge" style="{{themeStyle === 'blackWhite' ? 'background-color: ' + blackWhiteColors.black + '; color: white;' : ''}}">VIP</view>
      <view class="nav-card-content">
        <text class="nav-icon">🧹</text>
        <text class="nav-text">断舍离箱</text>
        <text class="nav-subtitle" wx:if="{{discardCount !== undefined}}">{{discardCount}}件</text>
      </view>
    </view>

    <!-- 待办物品箱卡片 -->
    <view class="nav-card vip-feature" bindtap="viewTodoBox" style="background-color: {{themeStyle === 'autumn' ? 'rgba(255, 255, 255, 0.7)' : (themeStyle === 'pinkBlue' ? 'rgba(255, 255, 255, 0.8)' : 'white')}}; color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueDark : blackWhiteColors.black)}}; position: relative; {{themeStyle === 'blackWhite' ? 'border: 1px solid ' + blackWhiteColors.black + ';' : ''}}">
      <!-- 今日待办红点提示 -->
      <view wx:if="{{hasTodayTodo}}" style="position: absolute; top: 10rpx; right: 10rpx; width: 16rpx; height: 16rpx; background-color: #FF4D4F; border-radius: 50%; box-shadow: 0 0 4rpx rgba(0,0,0,0.2);"></view>
      <view class="vip-badge" style="{{themeStyle === 'blackWhite' ? 'background-color: ' + blackWhiteColors.black + '; color: white;' : ''}}">VIP</view>
      <view class="nav-card-content">
        <text class="nav-icon">📝</text>
        <text class="nav-text">待办物品箱</text>
        <text class="nav-subtitle" wx:if="{{todoCount !== undefined}}">{{todoCount}}件</text>
        <!-- 显示最近待办 -->
        <view wx:if="{{recentTodo && recentTodoDate}}" class="recent-todo" style="margin-top: 8rpx; font-size: 24rpx; color: {{themeStyle === 'autumn' ? colors.olive_harvest : pinkBlueColors.blueMedium}};">
          <text class="recent-todo-date" style="padding: 2rpx 8rpx; border-radius: 6rpx; background-color: {{themeStyle === 'autumn' ? 'rgba(157, 145, 103, 0.2)' : 'rgba(203, 224, 249, 0.4)'}}; margin-right: 6rpx;">{{recentTodoDate}}</text>

        </view>
      </view>
    </view>

    <!-- 勋章卡片 -->
    <view class="nav-card" bindtap="viewMedals" style="background-color: {{themeStyle === 'autumn' ? 'rgba(255, 255, 255, 0.7)' : (themeStyle === 'pinkBlue' ? 'rgba(255, 255, 255, 0.8)' : 'white')}}; color: {{themeStyle === 'autumn' ? colors.spiced_wine : (themeStyle === 'pinkBlue' ? pinkBlueColors.blueDark : blackWhiteColors.black)}}; position: relative; {{themeStyle === 'blackWhite' ? 'border: 1px solid ' + blackWhiteColors.black + ';' : ''}}">
      <view class="nav-card-content">
        <text class="nav-icon">🎖</text>
        <text class="nav-text">我的勋章</text>
        <view class="medals-mini-display">
        </view>
      </view>
    </view>


  </view>

  <!-- VIP购买入口 - 右侧 -->
  <view class="contact-info contact-right-side" bindtap="copyXiaohongshuAccount" style="color: {{pageStyle.footerColor}}; position: fixed; right: 32rpx; bottom: calc(env(safe-area-inset-bottom) + 32rpx); background-color: rgba(255, 255, 255, 0.3); box-shadow: 0 2rpx 4rpx rgba(0, 0, 0, 0.1); padding: 8rpx 12rpx; border-radius: 12rpx; z-index: 10;">
    <text style="color: #ff2442; margin-right: 4rpx;">📖</text> <text style="font-weight: bold;">衣服扩容点我添加客服咨询！</text>
  </view>





  <!-- 任务卡片组件 -->
  <task-card
    wx:if="{{isTaskCardVisible}}"
    visible="{{isTaskCardVisible}}"
    themeStyle="{{themeStyle}}"
    tasks="{{dailyTasks.tasks}}"
    canDrawPrize="{{dailyTasks.canDrawPrize}}"
    rewardClaimed="{{dailyTasks.rewardClaimed}}"
    colors="{{colors}}"
    pinkBlueColors="{{pinkBlueColors}}"
    adLotteryCount="{{adLotteryData.lotteryCount}}"
    maxAdLotteryCount="{{adLotteryData.maxLotteryCount}}"
    bind:close="hideTaskCard"
    bind:drawPrize="handleDrawPrize"
    bind:adLotteryTap="showAdLottery"
  ></task-card>

  <!-- 主题任务详情卡片 -->
  <theme-task-card
    visible="{{themeTaskCard.visible}}"
    themeStyle="{{themeStyle}}"
    imageUrl="{{themeTaskCard.imageUrl}}"
    title="{{themeTaskCard.title}}"
    description="{{themeTaskCard.description}}"
    stepNumber="{{themeTaskCard.stepNumber}}"
    isCompleted="{{themeTaskCard.isCompleted}}"
    colors="{{colors}}"
    pinkBlueColors="{{pinkBlueColors}}"
    bind:close="hideThemeTaskCard"
  ></theme-task-card>

  <!-- 主题完成卡片 -->
  <theme-completion-card
    visible="{{themeCompletionVisible}}"
    themeStyle="{{themeStyle}}"
    colors="{{colors}}"
    pinkBlueColors="{{pinkBlueColors}}"
    blackWhiteColors="{{blackWhiteColors}}"
    memberDaysLeft="{{userInfo.memberDaysLeft}}"
    taskImages="{{themeTasks.taskImages}}"
    bind:close="hideThemeCompletionCard"
    bind:viewBenefits="showMemberBenefits"
  ></theme-completion-card>

  <!-- 奖励卡片 -->
  <view class="reward-card-modal {{rewardCardVisible ? 'visible' : 'hidden'}}" bindtap="closeRewardCard">
    <view class="reward-card-container" catchtap="stopPropagation" style="background-color: {{themeStyle === 'autumn' ? 'rgba(255, 255, 255, 0.95)' : 'rgba(255, 255, 255, 0.97)'}};">
      <!-- 标题 -->
      <view class="reward-card-header" style="color: {{themeStyle === 'autumn' ? colors.cowhide_cocoa : pinkBlueColors.blueDark}};">
        <view class="reward-card-title">

          <text>{{rewardData.title}}</text>
        </view>
      </view>

      <!-- 奖品图片 -->
      <view class="reward-card-image-container">
        <image class="reward-card-image" src="{{rewardData.image}}" mode="aspectFit"></image>
      </view>

      <!-- 奖品描述 -->
      <view class="reward-card-description" style="color: {{themeStyle === 'autumn' ? colors.spiced_wine : pinkBlueColors.pinkDark}};">
        {{rewardData.description}}
      </view>

      <!-- 确认按钮 -->
      <view class="reward-card-button-container">
        <view class="reward-card-button"
              bindtap="closeRewardCard"
              style="background-color: {{themeStyle === 'autumn' ? colors.toasted_caramel : pinkBlueColors.pinkMedium}};">
          <text class="reward-card-button-text">知道了</text>
        </view>
      </view>

      <!-- 关闭按钮 -->
      <view class="reward-card-close-button" catchtap="closeRewardCard">×</view>
    </view>
  </view>

  <!-- 签到奖励卡片 -->
  <view class="reward-card-modal {{checkInRewardVisible ? 'visible' : 'hidden'}}" bindtap="closeCheckInReward">
    <view class="reward-card-container checkin-reward-card" catchtap="stopPropagation" style="background-color: {{themeStyle === 'autumn' ? 'rgba(255, 255, 255, 0.95)' : 'rgba(255, 255, 255, 0.97)'}};">
      <!-- 关闭按钮 -->
      <view class="reward-card-close-button" catchtap="closeCheckInReward">×</view>

      <!-- 标题 -->
      <view class="reward-card-header" style="color: {{themeStyle === 'autumn' ? colors.cowhide_cocoa : pinkBlueColors.blueDark}};">
        <view class="reward-card-title">
          <text>连续签到{{checkInData.consecutiveDays}}天</text>
        </view>
      </view>

      <!-- 签到奖励图标 -->
      <view class="checkin-reward-icon-large">
        <text class="energy-icon">⚡</text>
      </view>

      <!-- 奖励数值 -->
      <view class="checkin-reward-value-large" style="color: {{themeStyle === 'autumn' ? colors.toasted_caramel : pinkBlueColors.pinkDark}};">
        +{{checkInRewardData.energy}}
      </view>

      <!-- 奖励说明 -->
      <view class="checkin-reward-desc">
        体力值
      </view>

      <!-- VIP翻倍提示 -->
      <view class="vip-double-tip" wx:if="{{checkInRewardData.isVIP}}">
        <text class="vip-icon">★</text>
        <text>VIP会员奖励翻倍</text>
      </view>

      <!-- 7天奖励提示 -->
      <view class="seven-day-tip">
        每日签到奖励10体力，每7天奖励2衣服容量
      </view>

      <!-- 确认按钮 -->
      <view class="reward-card-button-container">
        <view class="reward-card-button"
              bindtap="closeCheckInReward"
              style="background-color: {{themeStyle === 'autumn' ? colors.toasted_caramel : pinkBlueColors.pinkMedium}};">
          <text class="reward-card-button-text">知道了</text>
        </view>
      </view>
    </view>
  </view>

  <!-- 抽奖动画卡片 -->
  <view class="prize-drawing-modal {{isPrizeDrawingVisible ? 'visible' : 'hidden'}}">
    <view class="prize-drawing-title">抽奖中...</view>
    <view class="prize-drawing-container">
      <!-- 卡片滑动指示器 -->
      <view class="prize-drawing-indicator"></view>
      <view class="prize-spotlight"></view>
      <!-- 渐变遮罩 -->
      <view class="prize-drawing-mask"></view>
      <!-- 抽奖卡片容器 -->
      <view class="prize-cards-wrapper" animation="{{prizeCardsAnimation}}">
        <block wx:for="{{prizeDrawingCards}}" wx:key="index">
          <view class="prize-card {{currentCardIndex === index ? 'active' : ''}}" data-index="{{index}}">
            <view class="prize-card-number">{{index + 1}}</view>
            <image class="prize-card-image" src="{{item.imageUrl}}" mode="aspectFill"></image>
            <text class="prize-card-title">{{item.title || '任务' + (index + 1)}}</text>
          </view>
        </block>
      </view>
    </view>
  </view>
</view>

<!-- 会员权益弹窗 -->
<view class="member-benefits-modal" wx:if="{{showMemberBenefitsModal}}">
  <view class="member-benefits-content">
    <view class="member-benefits-header">
      <text class="member-benefits-title">VIP会员权益</text>
      <view class="member-benefits-close" bindtap="hideMemberBenefits">×</view>
    </view>

    <!-- 会员价格表 -->
    <view class="price-table-container">
      <view class="price-table">
        <view class="price-option {{selectedPriceOption === 'monthly' ? 'price-option-selected' : ''}}" bindtap="selectPriceOption" data-option="monthly">
          <view class="price-option-title">月度会员</view>
          <view class="price-option-price">2.88</view>
          <view class="price-option-unit">元/月</view>
          <view class="price-option-monthly">每月2.88元</view>
        </view>
        <view class="price-option {{selectedPriceOption === 'quarterly' ? 'price-option-selected' : ''}}" bindtap="selectPriceOption" data-option="quarterly">
          <view class="price-option-title">季度会员</view>
          <view class="price-option-price">7.99</view>
          <view class="price-option-unit">元/3个月</view>
          <view class="price-option-monthly">每月2.66元</view>
          <view class="price-option-tag">经济实惠</view>
        </view>
        <view class="price-option {{selectedPriceOption === 'yearly' ? 'price-option-selected' : ''}}" bindtap="selectPriceOption" data-option="yearly">
          <view class="price-option-title">年度会员</view>
          <view class="price-option-price-container">
            <view class="price-option-price-original">28</view>
            <view class="price-option-price">19.6</view>
          </view>
          <view class="price-option-unit">元/年</view>
          <view class="price-option-monthly">每月1.6元</view>
          <view class="price-option-tag price-option-tag-discount">限时七折</view>
        </view>
      </view>
    </view>

    <view class="member-benefits-body">
      <view class="benefit-item benefit-item-upload">
        <view class="benefit-icon">💾</view>
        <view class="benefit-content">
          <view class="benefit-title">批量上传</view>
          <view class="benefit-desc">一键批量上传衣物照片</view>
        </view>
      </view>
      <view class="benefit-item benefit-item-energy">
        <view class="benefit-icon">⚡</view>
        <view class="benefit-content">
          <view class="benefit-title">每月赠送200体力值</view>
          <view class="benefit-desc">使用AI功能无需担心体力不足</view>
        </view>
      </view>
      <view class="benefit-item benefit-item-checkin">
        <view class="benefit-icon">📅</view>
        <view class="benefit-content">
          <view class="benefit-title">每日签到双倍奖励</view>
          <view class="benefit-desc">签到获得双倍体力值奖励</view>
        </view>
      </view>
      <view class="benefit-item benefit-item-ad-free">
        <view class="benefit-icon">🚫</view>
        <view class="benefit-content">
          <view class="benefit-title">免广告体验</view>
          <view class="benefit-desc">享受无广告干扰的使用体验</view>
        </view>
      </view>
      <view class="benefit-item benefit-item-box">
        <view class="benefit-icon">💰</view>
        <view class="benefit-content">
          <view class="benefit-title">特殊衣物箱</view>
          <view class="benefit-desc">换季箱、断舍离箱、代办物品箱</view>
        </view>
      </view>
      <view class="benefit-item benefit-item-outfit">
        <view class="benefit-icon">👗</view>
        <view class="benefit-content">
          <view class="benefit-title">无限搭配</view>
          <view class="benefit-desc">创建无限数量的搭配组合</view>
        </view>
      </view>
      <view class="benefit-item benefit-item-wardrobe">
        <view class="benefit-icon">👟</view>
        <view class="benefit-content">
          <view class="benefit-title">衣柜统计分析</view>
          <view class="benefit-desc">对衣柜的统计和AI分析</view>
        </view>
      </view>
    </view>
    <view class="member-benefits-footer">
      <block wx:if="{{userInfo.memberType === 'VIP'}}">
        <view class="member-expire-info">
          <text>您的VIP会员将在 {{userInfo.memberDaysLeft}} 天后到期</text>
        </view>
        <view class="value-proposition">继续享受VIP会员权益，让您的衣柜管理更加高效便捷</view>
        <button class="open-vip-button renew-button" bindtap="showVipPurchase">
          <block wx:if="{{selectedPriceOption === 'monthly'}}">续费月度会员 ¥2.88</block>
          <block wx:elif="{{selectedPriceOption === 'quarterly'}}">续费季度会员 ¥7.99</block>
          <block wx:else><text class="original-price"></text> 续费年度会员 ¥19.6</block>
        </button>
      </block>
      <block wx:else>
        <view class="value-proposition">如果能帮助您少买一件衣服，一年的会员费就已经回本了</view>
        <button class="open-vip-button" bindtap="showVipPurchase">
          <block wx:if="{{selectedPriceOption === 'monthly'}}">开通月度会员 ¥2.88</block>
          <block wx:elif="{{selectedPriceOption === 'quarterly'}}">开通季度会员 ¥7.99</block>
          <block wx:else><text class="original-price">¥31</text> 开通年度会员 ¥21.7</block>
        </button>
      </block>
    </view>
  </view>
</view>

<!-- VIP购买弹窗 -->
<view class="vip-purchase-modal" wx:if="{{showVipPurchaseModal}}">
  <view class="vip-purchase-content">
    <view class="vip-purchase-header">
      <view class="vip-purchase-close" bindtap="hideVipPurchase">×</view>
    </view>
    <view class="vip-purchase-body">
      <image class="vip-purchase-image" src="{{vipPurchaseImage}}" mode="widthFix" show-menu-by-longpress="true" binderror="onVipPurchaseImageError"></image>
      <view class="vip-purchase-tip-card">
        <view class="vip-purchase-tip-title">会员特惠</view>
        <view class="vip-purchase-tip-content">
          <view class="vip-purchase-tip-item">长按图片可扫码添加客服微信</view>
          <view class="vip-purchase-tip-item premium-promo">
            <view class="top-border"></view>
            衣服扩容永不过期
          </view>
        </view>
      </view>
    </view>
  </view>
</view>

<!-- 体力值规则弹窗 -->
<view class="energy-rules-modal" wx:if="{{showEnergyRulesModal}}">
  <view class="energy-rules-content">
    <view class="energy-rules-header">
      <text class="energy-rules-title">猫咪体力值</text>
      <view class="energy-rules-close" bindtap="hideEnergyRules">×</view>
    </view>

    <!-- 体力值显示 -->
    <view class="energy-display">
      <view class="energy-bar-container">
        <!-- 体力值进度条 -->
        <view class="energy-bar-wrapper">
          <view class="energy-bar-bg"></view>
          <!-- 体力值低于50时的进度条 -->
          <view wx:if="{{userInfo.catEnergy < 50}}" class="energy-bar-progress" style="width: {{userInfo.catEnergy / 500 * 100}}%; background: linear-gradient(to right, #FF9800, #FFC107)"></view>

          <!-- 体力值介于50和200之间的进度条 -->
          <view wx:elif="{{userInfo.catEnergy >= 50 && userInfo.catEnergy <= 200}}" class="energy-bar-progress" style="width: {{userInfo.catEnergy / 500 * 100}}%; background: linear-gradient(to right, #4CAF50, #8BC34A)"></view>

          <!-- 体力值高于200的进度条 -->
          <view wx:else class="energy-bar-progress" style="width: {{userInfo.catEnergy / 500 * 100}}%; background: linear-gradient(to right, #2196F3, #03A9F4)"></view>

          <!-- 猫咪图片 -->
          <view class="energy-cat-container" style="left: calc({{userInfo.catEnergy / 500 * 100}}% - {{userInfo.catEnergy / 500 * 100 > 90 ? '100rpx' : (userInfo.catEnergy / 500 * 100 < 10 ? '0' : '50rpx')}})">
            <image wx:if="{{catImages.isLoaded && userInfo.catEnergy < 50}}" class="energy-cat-image" src="{{catImages.tireCat}}" mode="aspectFill"></image>
            <image wx:if="{{catImages.isLoaded && userInfo.catEnergy >= 50 && userInfo.catEnergy <= 200}}" class="energy-cat-image" src="{{catImages.normalCat}}" mode="aspectFill"></image>
            <image wx:if="{{catImages.isLoaded && userInfo.catEnergy > 200}}" class="energy-cat-image" src="{{catImages.happyCat}}" mode="aspectFill"></image>
          </view>
        </view>

        <!-- 体力值数字显示 -->
        <view class="energy-value-container">
          <view class="energy-value-large">{{userInfo.catEnergy}}</view>
          <view class="energy-max">/500</view>
        </view>
      </view>
    </view>

    <!-- 体力值使用规则 -->
    <view class="energy-rules-section">
      <view class="energy-rules-section-title">体力值使用规则</view>
      <view class="energy-rule-item energy-rule-item-extract">
        <view class="energy-rule-icon">👗</view>
        <view class="energy-rule-content">
          <view class="energy-rule-title">AI抠图一件衣服</view>
          <view class="energy-rule-desc">自动提取衣物轮廓，去除背景</view>
        </view>
        <view class="energy-rule-cost">2体力</view>
      </view>
      <view class="energy-rule-item energy-rule-item-outfit">
        <view class="energy-rule-icon">👙</view>
        <view class="energy-rule-content">
          <view class="energy-rule-title">AI创建搭配</view>
          <view class="energy-rule-desc">智能生成搭配组合推荐</view>
        </view>
        <view class="energy-rule-cost">5体力</view>
      </view>
      <view class="energy-rule-item energy-rule-item-analyze">
        <view class="energy-rule-icon">📈</view>
        <view class="energy-rule-content">
          <view class="energy-rule-title">AI分析衣柜</view>
          <view class="energy-rule-desc">智能分析衣柜结构和空缺</view>
        </view>
        <view class="energy-rule-cost">10体力</view>
      </view>
    </view>

    <!-- 体力值获取方式 -->
    <view class="energy-rules-section">
      <view class="energy-rules-section-title">体力值获取方式</view>
      <view class="energy-rule-item energy-rule-item-vip">
        <view class="energy-rule-icon">⭐</view>
        <view class="energy-rule-content">
          <view class="energy-rule-title">会员每月赠送</view>
          <view class="energy-rule-desc">VIP会员每月自动发放</view>
        </view>
        <view class="energy-rule-gain">200体力</view>
      </view>
      <view class="energy-rule-item energy-rule-item-task">
        <view class="energy-rule-icon">🎰</view>
        <view class="energy-rule-content">
          <view class="energy-rule-title">完成每日任务抽取卡片</view>
          <view class="energy-rule-desc">每日登录完成任务抽卡片</view>
        </view>
        <view class="energy-rule-gain">随机体力</view>
      </view>
      <view class="energy-rule-item energy-rule-item-ad">
        <view class="energy-rule-icon">📺</view>
        <view class="energy-rule-content">
          <view class="energy-rule-title">看广告得体力</view>
          <view class="energy-rule-desc">每日最多可看5次广告</view>
        </view>
        <view class="energy-rule-gain">10体力</view>

      </view>
    </view>
  </view>
</view>

<!-- 主题选择弹窗 -->
<view class="theme-selector-modal" wx:if="{{showThemeSelectorModal}}" bindtap="hideThemeSelector">
  <view class="theme-selector-content" catchtap="stopPropagation">
    <view class="theme-selector-header">
      <text class="theme-selector-title">选择外观风格</text>
      <view class="theme-selector-close" bindtap="hideThemeSelector">×</view>
    </view>

    <view class="theme-selector-options">
      <!-- 秋季主题选项 -->
      <view class="theme-option {{themeStyle === 'autumn' ? 'theme-option-selected' : ''}}"
            bindtap="selectTheme" data-theme="autumn">
        <view class="theme-option-preview">
          <view class="theme-preview-color" style="background-color: {{colors.golden_batter}}"></view>
          <view class="theme-preview-color" style="background-color: {{colors.spiced_wine}}"></view>
          <view class="theme-preview-color" style="background-color: {{colors.olive_harvest}}"></view>
        </view>
        <view class="theme-option-name">秋季风格</view>
        <view wx:if="{{themeStyle === 'autumn'}}" class="theme-option-check">✓</view>
      </view>

      <!-- 粉蓝主题选项 -->
      <view class="theme-option {{themeStyle === 'pinkBlue' ? 'theme-option-selected' : ''}}"
            bindtap="selectTheme" data-theme="pinkBlue">
        <view class="theme-option-preview">
          <view class="theme-preview-color" style="background-color: {{pinkBlueColors.pinkLight}}"></view>
          <view class="theme-preview-color" style="background-color: {{pinkBlueColors.pinkDark}}"></view>
          <view class="theme-preview-color" style="background-color: {{pinkBlueColors.blueMedium}}"></view>
        </view>
        <view class="theme-option-name">粉蓝风格</view>
        <view wx:if="{{themeStyle === 'pinkBlue'}}" class="theme-option-check">✓</view>
      </view>

      <!-- 黑白主题选项 -->
      <view class="theme-option {{themeStyle === 'blackWhite' ? 'theme-option-selected' : ''}}"
            bindtap="selectTheme" data-theme="blackWhite">
        <view class="theme-option-preview">
          <view class="theme-preview-color" style="background-color: {{blackWhiteColors.black}}"></view>
          <view class="theme-preview-color" style="background-color: {{blackWhiteColors.mediumGray}}"></view>
          <view class="theme-preview-color" style="background-color: {{blackWhiteColors.white}}"></view>
        </view>
        <view class="theme-option-name">黑白风格</view>
        <view wx:if="{{themeStyle === 'blackWhite'}}" class="theme-option-check">✓</view>
      </view>
    </view>
  </view>
</view>
