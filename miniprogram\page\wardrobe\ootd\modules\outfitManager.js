/**
 * 搭配管理模块
 * 负责OOTD页面搭配数据的获取和处理
 */

/**
 * 获取用户的所有搭配
 * @param {string} userOpenId - 用户的OpenID
 * @returns {Promise<Array>} 包含所有搭配的Promise
 */
function getAllOutfits(userOpenId) {
  return new Promise((resolve, reject) => {
    if (!userOpenId) {
      console.error('用户OpenID不能为空');
      reject(new Error('用户OpenID不能为空'));
      return;
    }

    console.log('开始获取用户所有搭配，用户ID:', userOpenId);

    const db = wx.cloud.database();
    const MAX_LIMIT = 20; // 单次查询最大记录数（微信小程序限制为20）

    // 先获取集合中的总记录数
    db.collection('outfits').where({
      _openid: userOpenId
    }).count().then(countResult => {
      const total = countResult.total;
      console.log('用户搭配总数:', total);

      // 如果没有搭配，直接返回空数组
      if (total === 0) {
        console.warn('用户没有搭配');
        resolve([]);
        return;
      }

      // 计算需要分几次获取
      const batchTimes = Math.ceil(total / MAX_LIMIT);
      console.log('需要分', batchTimes, '次获取所有搭配');

      // 创建查询任务数组
      const tasks = [];
      for (let i = 0; i < batchTimes; i++) {
        const promise = db.collection('outfits')
          .where({
            _openid: userOpenId
          })
          .skip(i * MAX_LIMIT)
          .limit(MAX_LIMIT)
          .get();
        tasks.push(promise);
      }

      // 等待所有查询完成
      Promise.all(tasks).then(results => {
        // 合并所有结果
        let outfits = [];
        for (let i = 0; i < results.length; i++) {
          outfits = outfits.concat(results[i].data);
        }

        console.log('获取用户搭配成功，总共:', outfits.length, '件');

        // 检查返回的数据是否有效
        if (!outfits || outfits.length === 0) {
          console.warn('用户没有搭配');
          resolve([]);
          return;
        }

        // 处理每个搭配数据
        const processedOutfits = outfits.map(outfit => {
          // 添加格式化后的日期
          if (outfit.createTime) {
            outfit.createTimeFormatted = formatDate(outfit.createTime);
          }

          // 确保outfit有id字段
          if (!outfit.id && outfit._id) {
            outfit.id = outfit._id;
          }

          // 确保outfit有类型字段
          if (!outfit.type && outfit.category) {
            outfit.type = outfit.category;
          } else if (!outfit.type && !outfit.category) {
            // 默认类型为日常
            outfit.type = 'daily';
            outfit.category = 'daily';
          }

          return outfit;
        });

        // 检查是否需要获取完整的衣物数据
        const needFetchClothes = processedOutfits.some(outfit =>
          outfit.items && Array.isArray(outfit.items) &&
          outfit.items.some(item => item.clothingId && (!item.name || !item.imageUrl))
        );

        if (needFetchClothes) {
          // 收集所有需要获取的衣物ID
          const allClothingIds = [];
          processedOutfits.forEach(outfit => {
            if (outfit.items && Array.isArray(outfit.items)) {
              outfit.items.forEach(item => {
                if (item && item.clothingId && (!item.name || !item.imageUrl)) {
                  allClothingIds.push(item.clothingId);
                }
              });
            }
          });

          if (allClothingIds.length > 0) {
            // 获取衣物数据
            return getClothesData(allClothingIds)
              .then(clothesData => {
                // 更新每个搭配的衣物项数据
                processedOutfits.forEach(outfit => {
                  if (outfit.items && Array.isArray(outfit.items)) {
                    outfit.items = outfit.items.map(item => {
                      if (item && item.clothingId) {
                        const clothingData = clothesData.find(c => c && c._id === item.clothingId);
                        if (clothingData) {
                          // 优先使用抠图后的图片 (processedImageFileID)
                          const imageFileID = clothingData.processedImageFileID || clothingData.imageFileID || clothingData.imageUrl || null;

                          // 合并衣物数据
                          return {
                            ...item,
                            name: item.name || clothingData.name,
                            type: item.type || clothingData.type || clothingData.category,
                            category: item.category || clothingData.category,
                            // 优先使用抠图后的图片URL或文件ID
                            imageUrl: item.imageUrl || clothingData.processedImageUrl || clothingData.imageUrl || clothingData.processedImageFileID,
                            // 保存原始fileID和抠图后的fileID用于后续刷新
                            imageFileID: imageFileID,
                            processedImageFileID: clothingData.processedImageFileID || null,
                            originalImageFileID: clothingData.imageFileID || null,
                            originalClothing: clothingData
                          };
                        }
                      }
                      return item;
                    });
                  }
                });

                // 处理所有搭配的图片URL
                return processAllOutfitsImageUrls(processedOutfits);
              });
          }
        }

        // 处理所有搭配的图片URL
        return processAllOutfitsImageUrls(processedOutfits);
      })
      .then(processedOutfits => {
        // 确保每个搭配都有预览图和默认图片
        processedOutfits.forEach(outfit => {
          ensureDefaultImages(outfit);
        });

        resolve(processedOutfits);
      })
      .catch(err => {
        console.error('获取用户搭配失败:', err);
        reject(err);
      });
    }).catch(err => {
      console.error('获取搭配总数失败:', err);
      reject(err);
    });
  });
}

/**
 * 获取衣物数据
 * @param {Array} clothingIds - 衣物ID数组
 * @returns {Promise<Array>} 包含衣物数据的Promise
 */
function getClothesData(clothingIds) {
  return new Promise((resolve, reject) => {
    if (!clothingIds || !Array.isArray(clothingIds) || clothingIds.length === 0) {
      resolve([]);
      return;
    }

    console.log('获取衣物数据，ID数量:', clothingIds.length);

    const db = wx.cloud.database();
    const _ = db.command;
    const MAX_LIMIT = 20; // 微信小程序限制一次最多查询20条数据

    // 如果ID数量超过限制，需要分批查询
    if (clothingIds.length <= MAX_LIMIT) {
      // 单次查询足够
      db.collection('clothes')
        .where({
          _id: _.in(clothingIds)
        })
        .get()
        .then(res => {
          console.log('获取衣物数据成功:', res.data);
          resolve(res.data || []);
        })
        .catch(err => {
          console.error('获取衣物数据失败:', err);
          resolve([]);
        });
    } else {
      // 需要分批查询
      console.log('衣物ID数量超过限制，需要分批查询');

      // 计算需要查询的批次
      const batchTimes = Math.ceil(clothingIds.length / MAX_LIMIT);
      console.log('需要分', batchTimes, '次查询');

      const tasks = [];

      // 创建每个批次的查询任务
      for (let i = 0; i < batchTimes; i++) {
        // 获取当前批次的ID数组
        const batchIds = clothingIds.slice(i * MAX_LIMIT, (i + 1) * MAX_LIMIT);

        const promise = db.collection('clothes')
          .where({
            _id: _.in(batchIds)
          })
          .get();

        tasks.push(promise);
      }

      // 等待所有查询完成
      Promise.all(tasks)
        .then(results => {
          // 合并所有结果
          let allClothes = [];
          results.forEach(res => {
            allClothes = allClothes.concat(res.data);
          });

          console.log('分批获取衣物数据成功，总数:', allClothes.length);
          resolve(allClothes);
        })
        .catch(err => {
          console.error('分批获取衣物数据失败:', err);
          resolve([]);
        });
    }
  });
}

/**
 * 处理所有搭配数据中的图片URL
 * @param {Array} outfits - 搭配数据数组
 * @returns {Promise<Array>} 处理后的搭配数据数组
 */
function processAllOutfitsImageUrls(outfits) {
  return new Promise((resolve, reject) => {
    if (!outfits || !Array.isArray(outfits) || outfits.length === 0) {
      resolve([]);
      return;
    }

    // 导入imageManager模块
    const imageManager = require('./imageManager');
    // 导入本地图片缓存模块
    const localImageCache = require('./localImageCache');

    // 收集所有需要获取临时URL的fileID
    const fileIDs = [];
    const fileIDToOutfitMap = new Map();

    // 遍历所有搭配
    outfits.forEach((outfit, outfitIndex) => {
      // 收集搭配预览图的fileID
      if (outfit.imageFileID && outfit.imageFileID.includes('cloud://')) {
        fileIDs.push(outfit.imageFileID);
        fileIDToOutfitMap.set(outfit.imageFileID, { type: 'preview', outfitIndex });
      } else if (outfit.previewImage && outfit.previewImage.includes('cloud://')) {
        fileIDs.push(outfit.previewImage);
        fileIDToOutfitMap.set(outfit.previewImage, { type: 'preview', outfitIndex });
      }

      // 收集搭配中每个衣物图片的fileID
      if (outfit.items && Array.isArray(outfit.items)) {
        outfit.items.forEach((item, itemIndex) => {
          // 优先使用抠图后的图片ID (processedImageFileID)
          const processedFileID = item.processedImageFileID;
          const originalFileID = item.imageFileID || item.originalImageFileID || item.fileID;

          // 优先使用抠图后的图片ID，如果没有，再使用原始图片ID
          if (processedFileID && processedFileID.includes('cloud://')) {
            fileIDs.push(processedFileID);
            fileIDToOutfitMap.set(processedFileID, {
              type: 'item',
              outfitIndex,
              itemIndex,
              isProcessed: true
            });
          } else if (originalFileID && originalFileID.includes('cloud://')) {
            fileIDs.push(originalFileID);
            fileIDToOutfitMap.set(originalFileID, {
              type: 'item',
              outfitIndex,
              itemIndex,
              isProcessed: false
            });
          } else if (item.imageUrl && item.imageUrl.includes('cloud://')) {
            fileIDs.push(item.imageUrl);
            fileIDToOutfitMap.set(item.imageUrl, {
              type: 'item',
              outfitIndex,
              itemIndex,
              isProcessed: false
            });
          }

          // 确保每个衣物项都有名称
          if (!item.name && item.clothingName) {
            item.name = item.clothingName;
          } else if (!item.name && item.originalClothing && item.originalClothing.name) {
            item.name = item.originalClothing.name;
          } else if (!item.name) {
            item.name = `衣物 ${itemIndex + 1}`;
          }

          // 确保每个衣物项都有类型
          if (!item.type && item.clothingType) {
            item.type = item.clothingType;
          } else if (!item.type && item.originalClothing && item.originalClothing.type) {
            item.type = item.originalClothing.type;
          } else if (!item.type && item.category) {
            item.type = item.category;
          }
        });
      } else {
        // 如果没有衣物项，创建一个空数组
        outfit.items = [];
      }
    });

    // 如果没有需要获取临时URL的fileID，直接返回原始数据
    if (fileIDs.length === 0) {
      console.log('没有需要获取临时URL的fileID');
      resolve(outfits);
      return;
    }

    console.log(`需要处理 ${fileIDs.length} 个图片URL`);

    // 使用本地图片缓存模块批量处理图片
    localImageCache.batchProcessImages(fileIDs)
      .then(fileIDToURL => {
        console.log('图片处理完成，开始更新搭配数据');

        // 更新每个搭配的图片URL
        fileIDs.forEach(fileID => {
          const url = fileIDToURL[fileID];
          if (!url) return;  // 跳过无法获取URL的图片

          const info = fileIDToOutfitMap.get(fileID);
          if (!info) return;

          const { type, outfitIndex, itemIndex, isProcessed } = info;
          const outfit = outfits[outfitIndex];

          if (type === 'preview') {
            // 更新搭配预览图
            outfit.previewImage = url;
          } else if (type === 'item' && outfit.items && outfit.items[itemIndex]) {
            // 更新衣物图片
            outfit.items[itemIndex].imageUrl = url;

            // 额外记录是否使用了抠图后的图片
            if (isProcessed) {
              outfit.items[itemIndex].isUsingProcessedImage = true;
            }
          }
        });

        // 确保每个搭配和衣物项都有默认图片
        outfits.forEach(outfit => {
          // 如果没有预览图，设置默认预览图
          if (!outfit.previewImage) {
            outfit.previewImage = imageManager.getDefaultPreviewImageUrl();
          }

          // 确保每个衣物项都有图片
          if (outfit.items && Array.isArray(outfit.items)) {
            outfit.items.forEach(item => {
              if (!item.imageUrl) {
                item.imageUrl = imageManager.getDefaultItemImageUrl();
              }
            });
          }
        });

        console.log('搭配数据图片URL更新完成');
        resolve(outfits);
      })
      .catch(err => {
        console.error('处理图片URL出错:', err);

        // 出错时，确保每个搭配和衣物项都有默认图片
        outfits.forEach(outfit => {
          // 如果没有预览图，设置默认预览图
          if (!outfit.previewImage) {
            outfit.previewImage = imageManager.getDefaultPreviewImageUrl();
          }

          // 确保每个衣物项都有图片
          if (outfit.items && Array.isArray(outfit.items)) {
            outfit.items.forEach(item => {
              if (!item.imageUrl) {
                item.imageUrl = imageManager.getDefaultItemImageUrl();
              }
            });
          }
        });

        resolve(outfits);
      });
  });
}

/**
 * 确保搭配有默认图片
 * @param {Object} outfitData - 搭配数据
 */
function ensureDefaultImages(outfitData) {
  // 导入imageManager模块获取默认图片
  const imageManager = require('./imageManager');

  // 确保有预览图
  if (!outfitData.previewImage) {
    // 如果有衣物项，尝试使用第一个衣物的图片作为预览图
    if (outfitData.items && Array.isArray(outfitData.items) && outfitData.items.length > 0) {
      const firstItemWithImage = outfitData.items.find(item => item && item.imageUrl);
      if (firstItemWithImage) {
        outfitData.previewImage = firstItemWithImage.imageUrl;
      } else {
        outfitData.previewImage = imageManager.getDefaultPreviewImageUrl();
      }
    } else {
      outfitData.previewImage = imageManager.getDefaultPreviewImageUrl();
    }
  }

  // 确保每个衣物项都有图片
  if (outfitData.items && Array.isArray(outfitData.items)) {
    outfitData.items.forEach(item => {
      if (!item.imageUrl) {
        item.imageUrl = imageManager.getDefaultItemImageUrl();
      }
    });
  }
}

/**
 * 获取当天的穿搭记录
 * @param {string} userOpenId - 用户的OpenID
 * @returns {Promise<Object>} 包含当天穿搭记录的Promise
 */
function getTodayOutfit(userOpenId) {
  return new Promise((resolve, reject) => {
    if (!userOpenId) {
      console.error('用户OpenID不能为空');
      reject(new Error('用户OpenID不能为空'));
      return;
    }

    console.log('开始获取今日穿搭记录，用户ID:', userOpenId);

    const db = wx.cloud.database();
    const _ = db.command;

    // 获取今天的开始时间（当天 00:00:00）
    const todayStart = new Date();
    todayStart.setHours(0, 0, 0, 0);

    // 获取今天的结束时间（当天 23:59:59）
    const todayEnd = new Date();
    todayEnd.setHours(23, 59, 59, 999);

    console.log('查询今天的时间范围:', todayStart, '至', todayEnd);

    // 查询条件：用户今天的穿搭记录（精确到当天）
    db.collection('dailyOutfits')
      .where({
        _openid: userOpenId,
        date: _.gte(todayStart).and(_.lte(todayEnd))
      })
      .orderBy('date', 'desc')
      .limit(1)
      .get()
      .then(res => {
        console.log('获取今日穿搭记录成功:', res.data);

        if (!res.data || res.data.length === 0) {
          console.log('今天没有穿搭记录');
          resolve(null);
          return;
        }

        const todayOutfit = res.data[0];

        // 验证这确实是今天的记录
        const outfitDate = new Date(todayOutfit.date);
        const isToday = outfitDate >= todayStart && outfitDate <= todayEnd;

        if (!isToday) {
          console.warn('获取到的记录不是今天的:', outfitDate);
          resolve(null);
          return;
        }

        console.log('确认这是今天的穿搭记录:', todayOutfit);
        resolve(todayOutfit);
      })
      .catch(err => {
        console.error('获取今日穿搭记录失败:', err);
        reject(err);
      });
  });
}

/**
 * 获取特定日期的穿搭记录
 * @param {string} userOpenId - 用户的OpenID
 * @param {string} dateStr - 日期字符串（格式：YYYY-MM-DD）
 * @returns {Promise<Object>} 包含特定日期穿搭记录的Promise
 */
function getOutfitByDate(userOpenId, dateStr) {
  return new Promise((resolve, reject) => {
    if (!userOpenId) {
      console.error('用户OpenID不能为空');
      reject(new Error('用户OpenID不能为空'));
      return;
    }

    if (!dateStr) {
      console.error('日期不能为空');
      reject(new Error('日期不能为空'));
      return;
    }

    console.log('开始获取特定日期穿搭记录，用户ID:', userOpenId, '日期:', dateStr);

    const db = wx.cloud.database();
    const _ = db.command;

    // 解析日期字符串
    const [year, month, day] = dateStr.split('-').map(Number);

    // 创建指定日期的开始时间和结束时间
    const startDate = new Date(year, month - 1, day, 0, 0, 0, 0);
    const endDate = new Date(year, month - 1, day, 23, 59, 59, 999);

    console.log('查询日期范围:', startDate, '至', endDate);

    // 查询条件：用户指定日期的穿搭记录
    db.collection('dailyOutfits')
      .where({
        _openid: userOpenId,
        date: _.gte(startDate).and(_.lte(endDate))
      })
      .orderBy('date', 'desc')
      .limit(1)
      .get()
      .then(res => {
        console.log('获取特定日期穿搭记录成功:', res.data);

        if (!res.data || res.data.length === 0) {
          console.log('该日期没有穿搭记录:', dateStr);
          resolve(null);
          return;
        }

        const outfit = res.data[0];
        resolve(outfit);
      })
      .catch(err => {
        console.error('获取特定日期穿搭记录失败:', err, '日期:', dateStr);
        reject(err);
      });
  });
}

/**
 * 更新衣物的穿着统计数据
 * @param {Array} items - 衣物项数组
 * @returns {Promise<void>} Promise
 */
function updateClothingWearStats(items) {
  return new Promise(async (resolve, reject) => {
    if (!items || !Array.isArray(items) || items.length === 0) {
      console.log('没有衣物需要更新穿着统计');
      resolve();
      return;
    }

    const db = wx.cloud.database();
    const _ = db.command;

    try {
      console.log('开始更新衣物穿着统计，衣物数量:', items.length);

      // 获取今天的日期
      const today = new Date();
      today.setHours(0, 0, 0, 0);

      // 格式化日期为字符串格式: YYYY-MM-DD
      const formattedDate = formatDate(today);
      console.log('格式化后的穿着日期:', formattedDate);

      // 收集所有需要更新的衣物ID
      const clothingIds = [];

      items.forEach(item => {
        // 检查是否有clothingId
        if (item.clothingId) {
          clothingIds.push(item.clothingId);
        // 尝试从originalClothing中获取ID
        } else if (item.originalClothing && item.originalClothing._id) {
          clothingIds.push(item.originalClothing._id);
        }
      });

      if (clothingIds.length === 0) {
        console.log('没有有效的衣物ID，跳过更新穿着统计');
        resolve();
        return;
      }

      console.log('需要更新的衣物ID:', clothingIds);

      // 更新每个衣物的穿着次数和最近穿着时间
      const updatePromises = clothingIds.map(clothingId => {
        return db.collection('clothes')
          .doc(clothingId)
          .update({
            data: {
              // 穿着次数+1
              wornCount: _.inc(1),
              // 更新最近穿着时间为格式化的字符串
              lastWornDate: formattedDate
            }
          })
          .then(res => {
            console.log(`衣物[${clothingId}]穿着统计更新成功:`, res);
            return res;
          })
          .catch(err => {
            console.error(`衣物[${clothingId}]穿着统计更新失败:`, err);
            // 单个衣物更新失败不影响整体流程
            return null;
          });
      });

      // 等待所有更新完成
      await Promise.all(updatePromises);
      console.log('所有衣物穿着统计更新完成');
      resolve();
    } catch (err) {
      console.error('更新衣物穿着统计时发生错误:', err);
      // 出错时也视为完成，不影响主流程
      resolve();
    }
  });
}

/**
 * 获取完整的穿搭数据（包括衣物详情）
 * @param {string} outfitId - 穿搭ID
 * @returns {Promise<Object>} 包含完整穿搭数据的Promise
 */
function getCompleteOutfitData(outfitId) {
  return new Promise((resolve, reject) => {
    if (!outfitId) {
      console.error('穿搭ID不能为空');
      reject(new Error('穿搭ID不能为空'));
      return;
    }

    const db = wx.cloud.database();

    db.collection('outfits')
      .doc(outfitId)
      .get()
      .then(res => {
        if (!res.data) {
          throw new Error('未找到穿搭数据');
        }

        const outfitData = res.data;
        resolve(outfitData);
      })
      .catch(err => {
        console.error('获取完整穿搭数据失败:', err);
        reject(err);
      });
  });
}

/**
 * 保存今日穿搭记录
 * @param {Object} outfitData - 穿搭数据
 * @param {string} userOpenId - 用户的OpenID
 * @param {string} existingRecordId - 现有记录ID（如果要更新）
 * @param {boolean} isSingleClothing - 是否是单件衣物作为穿搭
 * @param {boolean} isDirectUpload - 是否是直接上传的图片作为穿搭
 * @param {boolean} isMultipleClothings - 是否是多件衣物组合作为穿搭
 * @returns {Promise<Object>} 包含保存结果的Promise
 */
function saveTodayOutfit(outfitData, userOpenId, existingRecordId, isSingleClothing = false, isDirectUpload = false, isMultipleClothings = false) {
  return new Promise(async (resolve, reject) => {
    if (!outfitData || !userOpenId) {
      console.error('穿搭数据或用户ID不能为空');
      reject(new Error('穿搭数据或用户ID不能为空'));
      return;
    }
    // 移除这行代码，因为它会覆盖传入的existingRecordId参数
    // const existingRecordId = '';

    console.log('保存今日穿搭记录:', outfitData, '是否单件衣物:', isSingleClothing, '是否直接上传图片:', isDirectUpload, '是否多件衣物:', isMultipleClothings);

    const db = wx.cloud.database();

    // 获取今天的日期
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // 准备要保存的数据
    const saveData = {
      outfitId: outfitData._id || outfitData.id,
      outfitName: outfitData.name,
      outfitType: outfitData.type || outfitData.category,
      date: today,
      //_openid: userOpenId,
      createTime: db.serverDate()
    };

    // 如果是单件衣物，添加相关标记和ID
    if (isSingleClothing) {
      saveData.isSingleClothing = true;
      saveData.singleClothingId = outfitData._id || outfitData.id;
      saveData.clothingImageFileID = outfitData.imageFileID || outfitData.processedImageFileID;
    }

    // 如果是多件衣物组合，添加相关标记和数据
    if (isMultipleClothings) {
      saveData.isMultipleClothings = true;
      saveData.clothingIds = outfitData.clothingIds || [];
      saveData.outfitItems = outfitData.items || [];
    }

    // 如果是直接上传的图片，添加相关标记和图片ID
    if (isDirectUpload) {
      saveData.isDirectUpload = true;
      saveData.directUploadImageFileID = outfitData.imageFileID;
    }

    try {
      // 如果没有提供现有记录ID，先检查今天是否已有穿搭记录
      if (!existingRecordId) {
        const _ = db.command;

        // 获取今天的结束时间（当天 23:59:59）
        const todayEnd = new Date();
        todayEnd.setHours(23, 59, 59, 999);

        console.log('查询今天的时间范围:', today, '至', todayEnd);

        // 查询今天是否已有穿搭记录（精确到当天）
        const existingRecords = await db.collection('dailyOutfits')
          .where({
            _openid: userOpenId,
            date: _.gte(today).and(_.lte(todayEnd))
          })
          .orderBy('date', 'desc')
          .limit(1)
          .get();

        if (existingRecords.data && existingRecords.data.length > 0) {
          // 如果已有记录，获取第一条记录的ID
          const record = existingRecords.data[0];
          const recordDate = new Date(record.date);

          // 验证这确实是今天的记录
          const isToday = recordDate >= today && recordDate <= todayEnd;

          if (isToday) {
            existingRecordId = record._id;
            console.log('找到今天已有穿搭记录:', existingRecordId);
          } else {
            console.warn('找到的记录不是今天的:', recordDate);
          }
        }
      }

      // 处理不同类型的穿搭数据
      let completeOutfitData = outfitData;

      if (isMultipleClothings) {
        // 如果是多件衣物组合，更新所有衣物的穿着次数
        console.log('多件衣物组合作为穿搭，更新所有衣物穿着次数');

        try {
          const clothingIds = saveData.clothingIds || [];

          for (const clothingId of clothingIds) {
            if (clothingId) {
              // 获取衣物当前数据
              const clothingData = await db.collection('clothes').doc(clothingId).get();

              if (clothingData && clothingData.data) {
                const currentWearCount = clothingData.data.wearCount || 0;

                // 更新穿着次数
                await db.collection('clothes').doc(clothingId).update({
                  data: {
                    wearCount: currentWearCount + 1,
                    lastWearDate: db.serverDate()
                  }
                });

                console.log('更新衣物穿着次数成功:', clothingId);
              }
            }
          }
        } catch (err) {
          console.error('更新多件衣物穿着次数失败:', err);
        }
      } else if (isSingleClothing) {
        // 如果是单件衣物，直接更新该衣物的穿着次数
        console.log('单件衣物作为穿搭，不需要获取完整穿搭数据');

        // 更新单件衣物的穿着次数
        try {
          const clothingId = saveData.singleClothingId;

          if (clothingId) {
            // 获取衣物当前数据
            const clothingData = await db.collection('clothes').doc(clothingId).get();

            if (clothingData && clothingData.data) {
              const currentWearCount = clothingData.data.wearCount || 0;

              // 更新穿着次数
              await db.collection('clothes').doc(clothingId).update({
                data: {
                  wearCount: currentWearCount + 1,
                  lastWearDate: db.serverDate()
                }
              });

              console.log('更新单件衣物穿着次数成功');
            }
          }
        } catch (err) {
          console.error('更新单件衣物穿着次数失败:', err);
        }
      } else {
        // 只有在不是单件衣物或多件衣物组合的情况下，才尝试获取完整的穿搭数据
        if (!outfitData.items || outfitData.items.length === 0) {
          try {
            completeOutfitData = await getCompleteOutfitData(saveData.outfitId);
            console.log('获取到完整的穿搭数据:', completeOutfitData);
          } catch (err) {
            console.warn('无法获取完整穿搭数据，将使用原始数据:', err);
          }
        }

        // 更新衣物的穿着统计数据（不阻塞主流程）
        if (completeOutfitData.items && completeOutfitData.items.length > 0) {
          updateClothingWearStats(completeOutfitData.items)
            .then(() => {
              console.log('衣物穿着统计更新成功');
            })
            .catch(err => {
              console.error('衣物穿着统计更新失败:', err);
            });
        }
      }

      // 如果有现有记录，则更新
      if (existingRecordId) {
        // 准备更新数据
        const updateData = {
          outfitId: saveData.outfitId,
          outfitName: saveData.outfitName,
          outfitType: saveData.outfitType,
          updateTime: db.serverDate()
        };

        // 如果是单件衣物，添加相关字段
        if (isSingleClothing) {
          updateData.isSingleClothing = true;
          updateData.singleClothingId = saveData.singleClothingId;
          updateData.clothingImageFileID = saveData.clothingImageFileID;
          // 清除多件衣物相关字段
          updateData.isMultipleClothings = false;
          updateData.clothingIds = [];
          updateData.outfitItems = [];
        } else if (isMultipleClothings) {
          // 如果是多件衣物组合，添加相关字段
          updateData.isMultipleClothings = true;
          updateData.clothingIds = saveData.clothingIds;
          updateData.outfitItems = saveData.outfitItems;
          // 清除单件衣物相关字段
          updateData.isSingleClothing = false;
          updateData.singleClothingId = '';
          updateData.clothingImageFileID = '';
        } else {
          // 如果不是单件衣物或多件衣物组合，确保清除相关标记
          updateData.isSingleClothing = false;
          updateData.singleClothingId = '';
          updateData.clothingImageFileID = '';
          updateData.isMultipleClothings = false;
          updateData.clothingIds = [];
          updateData.outfitItems = [];
        }

        db.collection('dailyOutfits')
          .doc(existingRecordId)
          .update({
            data: updateData
          })
          .then(res => {
            console.log('更新今日穿搭成功:', res);
            resolve({
              success: true,
              message: '今日穿搭已更新',
              data: {
                ...saveData,
                _id: existingRecordId
              }
            });
          })
          .catch(err => {
            console.error('更新今日穿搭失败:', err);
            reject(err);
          });
      } else {
        // 否则新建记录

        // 注意：直接上传的图片不是实际的衣物记录，所以不需要更新穿着次数
        // 如果将来直接上传的图片保存为真实衣物，可以在这里添加更新穿着次数的逻辑

        db.collection('dailyOutfits')
          .add({
            data: saveData
          })
          .then(res => {
            console.log('保存今日穿搭成功:', res);
            resolve({
              success: true,
              message: '今日穿搭已保存',
              data: {
                ...saveData,
                _id: res._id
              }
            });
          })
          .catch(err => {
            console.error('保存今日穿搭失败:', err);
            reject(err);
          });
      }
    } catch (err) {
      console.error('保存今日穿搭过程中发生错误:', err);
      reject(err);
    }
  });
}

/**
 * 格式化日期
 * @param {Date|string|number} date - 日期对象、日期字符串或时间戳
 * @returns {string} 格式化后的日期字符串，格式为YYYY-MM-DD
 */
function formatDate(date) {
  if (!date) return '';

  // 如果是日期对象，直接使用
  let dateObj = date;

  // 如果是时间戳字符串，转为数字
  if (typeof date === 'string') {
    dateObj = new Date(isNaN(date) ? date : parseInt(date));
  }

  // 如果是数字时间戳，转为日期对象
  if (typeof date === 'number') {
    dateObj = new Date(date);
  }

  // 获取年月日
  const year = dateObj.getFullYear();
  const month = (dateObj.getMonth() + 1).toString().padStart(2, '0'); // 补零，确保两位数
  const day = dateObj.getDate().toString().padStart(2, '0'); // 补零，确保两位数

  // 返回格式化的日期字符串：YYYY-MM-DD
  return `${year}-${month}-${day}`;
}

/**
 * 生成模拟搭配数据
 * @returns {Array} 模拟搭配数据数组
 */
function generateMockOutfits() {
  return [
    {
      id: 'outfit-daily-1',
      name: '舒适日常搭配',
      type: 'daily',
      category: 'daily',
      createTimeFormatted: formatDate(new Date()),
      previewImage: '/image/outfit-icon-HL.png',
      items: [
        { id: 'item1', name: '白色T恤', type: 'top', imageUrl: '/image/short-dress.png' },
        { id: 'item2', name: '牛仔裤', type: 'bottom', imageUrl: '/image/outfit-icon.png' }
      ]
    },
    {
      id: 'outfit-work-1',
      name: '职业商务装',
      type: 'work',
      category: 'work',
      createTimeFormatted: formatDate(new Date()),
      previewImage: '/image/outfit-icon.png',
      items: [
        { id: 'item3', name: '白衬衫', type: 'top', imageUrl: '/image/wardrobe-icon.png' },
        { id: 'item4', name: '西装裤', type: 'bottom', imageUrl: '/image/short-dress.png' }
      ]
    },
    {
      id: 'outfit-party-1',
      name: '派对时尚装',
      type: 'party',
      category: 'party',
      createTimeFormatted: formatDate(new Date()),
      previewImage: '/image/wardrobe-icon-HL.png',
      items: [
        { id: 'item5', name: '连衣裙', type: 'dress', imageUrl: '/image/short-dress-HL.png' },
        { id: 'item6', name: '高跟鞋', type: 'shoes', imageUrl: '/image/wardrobe-icon.png' }
      ]
    },
    {
      id: 'outfit-sport-1',
      name: '运动休闲装',
      type: 'sport',
      category: 'sport',
      createTimeFormatted: formatDate(new Date()),
      previewImage: '/image/outfit-icon.png',
      items: [
        { id: 'item7', name: '运动T恤', type: 'top', imageUrl: '/image/short-dress.png' },
        { id: 'item8', name: '运动裤', type: 'bottom', imageUrl: '/image/wardrobe-icon.png' }
      ]
    },
    {
      id: 'outfit-seasonal-1',
      name: '秋季休闲装',
      type: 'seasonal',
      category: 'seasonal',
      createTimeFormatted: formatDate(new Date()),
      previewImage: '/image/short-dress.png',
      items: [
        { id: 'item9', name: '风衣', type: 'outerwear', imageUrl: '/image/outfit-icon-HL.png' },
        { id: 'item10', name: '长裤', type: 'bottom', imageUrl: '/image/wardrobe-icon-HL.png' }
      ]
    },
    {
      id: 'outfit-daily-2',
      name: '休闲日常装',
      type: 'daily',
      category: 'daily',
      createTimeFormatted: formatDate(new Date()),
      previewImage: '/image/short-dress-HL.png',
      items: [
        { id: 'item11', name: '卫衣', type: 'top', imageUrl: '/image/short-dress.png' },
        { id: 'item12', name: '休闲裤', type: 'bottom', imageUrl: '/image/outfit-icon.png' }
      ]
    }
  ];
}

/**
 * 保存穿搭记录到指定日期
 * @param {Object} outfitData - 穿搭数据
 * @param {string} userOpenId - 用户的OpenID
 * @param {string|Date} targetDate - 目标日期（字符串格式YYYY-MM-DD或Date对象）
 * @param {string} existingRecordId - 现有记录ID（如果要更新）
 * @param {boolean} isSingleClothing - 是否是单件衣物作为穿搭
 * @param {boolean} isDirectUpload - 是否是直接上传的图片作为穿搭
 * @param {boolean} isMultipleClothings - 是否是多件衣物组合作为穿搭
 * @returns {Promise<Object>} 包含保存结果的Promise
 */
function saveOutfitToDate(outfitData, userOpenId, targetDate, existingRecordId, isSingleClothing = false, isDirectUpload = false, isMultipleClothings = false) {
  return new Promise(async (resolve, reject) => {
    if (!outfitData || !userOpenId || !targetDate) {
      console.error('穿搭数据、用户ID或目标日期不能为空');
      reject(new Error('穿搭数据、用户ID或目标日期不能为空'));
      return;
    }

    console.log('保存穿搭记录到指定日期:', targetDate, outfitData, '是否单件衣物:', isSingleClothing, '是否多件衣物:', isMultipleClothings);

    const db = wx.cloud.database();

    // 处理目标日期
    let dateObj;
    if (typeof targetDate === 'string') {
      // 如果是字符串格式（YYYY-MM-DD），转换为Date对象
      const [year, month, day] = targetDate.split('-').map(Number);
      dateObj = new Date(year, month - 1, day);
    } else if (targetDate instanceof Date) {
      // 如果已经是Date对象，直接使用
      dateObj = new Date(targetDate);
    } else {
      console.error('无效的日期格式');
      reject(new Error('无效的日期格式'));
      return;
    }

    // 确保只保留日期部分，去掉时间
    dateObj.setHours(0, 0, 0, 0);

    // 准备要保存的数据
    const saveData = {
      outfitId: outfitData._id || outfitData.id,
      outfitName: outfitData.name,
      outfitType: outfitData.type || outfitData.category,
      date: dateObj,
      createTime: db.serverDate()
    };

    // 如果是单件衣物，添加相关标记和ID
    if (isSingleClothing) {
      saveData.isSingleClothing = true;
      saveData.singleClothingId = outfitData._id || outfitData.id;
      saveData.clothingImageFileID = outfitData.imageFileID || outfitData.processedImageFileID;
    }

    // 如果是多件衣物组合，添加相关标记和数据
    if (isMultipleClothings) {
      saveData.isMultipleClothings = true;
      saveData.clothingIds = outfitData.clothingIds || [];
      saveData.outfitItems = outfitData.items || [];
    }

    // 如果是直接上传的图片，添加相关标记和图片ID
    if (isDirectUpload) {
      saveData.isDirectUpload = true;
      saveData.directUploadImageFileID = outfitData.imageFileID;
    }

    try {
      // 首先检查该日期是否已有穿搭记录
      if (!existingRecordId) {
        const startDate = new Date(dateObj);
        startDate.setHours(0, 0, 0, 0);

        const endDate = new Date(dateObj);
        endDate.setHours(23, 59, 59, 999);

        const _ = db.command;

        // 查询该日期是否已有穿搭记录
        const existingRecords = await db.collection('dailyOutfits')
          .where({
            _openid: userOpenId,
            date: _.gte(startDate).and(_.lte(endDate))
          })
          .get();

        if (existingRecords.data && existingRecords.data.length > 0) {
          // 如果已有记录，获取第一条记录的ID
          existingRecordId = existingRecords.data[0]._id;
          console.log('找到该日期已有穿搭记录:', existingRecordId);
        }
      }

      // 处理不同类型的穿搭数据
      if (isMultipleClothings) {
        // 如果是多件衣物组合，更新所有衣物的穿着次数
        console.log('多件衣物组合作为穿搭，更新所有衣物穿着次数');

        try {
          const clothingIds = saveData.clothingIds || [];

          for (const clothingId of clothingIds) {
            if (clothingId) {
              // 获取衣物当前数据
              const clothingData = await db.collection('clothes').doc(clothingId).get();

              if (clothingData && clothingData.data) {
                const currentWearCount = clothingData.data.wearCount || 0;

                // 更新穿着次数
                await db.collection('clothes').doc(clothingId).update({
                  data: {
                    wearCount: currentWearCount + 1,
                    lastWearDate: db.serverDate()
                  }
                });

                console.log('更新衣物穿着次数成功:', clothingId);
              }
            }
          }
        } catch (err) {
          console.error('更新多件衣物穿着次数失败:', err);
        }
      } else if (isSingleClothing) {
        // 如果是单件衣物，直接更新该衣物的穿着次数
        console.log('单件衣物作为穿搭，不需要获取完整穿搭数据');

        // 更新单件衣物的穿着次数
        try {
          const clothingId = saveData.singleClothingId;

          if (clothingId) {
            // 获取衣物当前数据
            const clothingData = await db.collection('clothes').doc(clothingId).get();

            if (clothingData && clothingData.data) {
              const currentWearCount = clothingData.data.wearCount || 0;

              // 更新穿着次数
              await db.collection('clothes').doc(clothingId).update({
                data: {
                  wearCount: currentWearCount + 1,
                  lastWearDate: db.serverDate()
                }
              });

              console.log('更新单件衣物穿着次数成功');
            }
          }
        } catch (err) {
          console.error('更新单件衣物穿着次数失败:', err);
        }
      } else {
        // 只有在不是单件衣物或多件衣物组合的情况下，才尝试获取完整的穿搭数据
        if (!outfitData.items || outfitData.items.length === 0) {
          try {
            const completeOutfitData = await getCompleteOutfitData(saveData.outfitId);
            console.log('获取到完整的穿搭数据:', completeOutfitData);

            // 更新衣物的穿着统计数据（不阻塞主流程）
            if (completeOutfitData.items && completeOutfitData.items.length > 0) {
              updateClothingWearStats(completeOutfitData.items)
                .then(() => {
                  console.log('衣物穿着统计更新成功');
                })
                .catch(err => {
                  console.error('衣物穿着统计更新失败:', err);
                });
            }
          } catch (err) {
            console.warn('无法获取完整穿搭数据，将使用原始数据:', err);
          }
        }
      }

      // 如果有现有记录，则更新
      if (existingRecordId) {
        // 准备更新数据
        const updateData = {
          outfitId: saveData.outfitId,
          outfitName: saveData.outfitName,
          outfitType: saveData.outfitType,
          updateTime: db.serverDate()
        };

        // 如果是单件衣物，添加相关字段
        if (isSingleClothing) {
          updateData.isSingleClothing = true;
          updateData.singleClothingId = saveData.singleClothingId;
          updateData.clothingImageFileID = saveData.clothingImageFileID;
          // 清除多件衣物相关字段
          updateData.isMultipleClothings = false;
          updateData.clothingIds = [];
          updateData.outfitItems = [];
        } else if (isMultipleClothings) {
          // 如果是多件衣物组合，添加相关字段
          updateData.isMultipleClothings = true;
          updateData.clothingIds = saveData.clothingIds;
          updateData.outfitItems = saveData.outfitItems;
          // 清除单件衣物相关字段
          updateData.isSingleClothing = false;
          updateData.singleClothingId = '';
          updateData.clothingImageFileID = '';
        } else {
          // 如果不是单件衣物或多件衣物组合，确保清除相关标记
          updateData.isSingleClothing = false;
          updateData.singleClothingId = '';
          updateData.clothingImageFileID = '';
          updateData.isMultipleClothings = false;
          updateData.clothingIds = [];
          updateData.outfitItems = [];
        }

        db.collection('dailyOutfits')
          .doc(existingRecordId)
          .update({
            data: updateData
          })
          .then(res => {
            console.log('更新指定日期穿搭成功:', res);

            // 检查是否是跨月份操作
            const today = new Date();
            const targetYear = dateObj.getFullYear();
            const targetMonth = dateObj.getMonth() + 1;
            const currentYear = today.getFullYear();
            const currentMonth = today.getMonth() + 1;

            // 如果目标日期不是当前月份，设置跨月份穿搭添加标记
            if (targetYear !== currentYear || targetMonth !== currentMonth) {
              console.log('检测到跨月份穿搭更新，设置标记');
              wx.setStorageSync('crossMonthOutfitAdded', true);
            }

            resolve({
              success: true,
              message: '穿搭已更新',
              data: {
                ...saveData,
                _id: existingRecordId
              }
            });
          })
          .catch(err => {
            console.error('更新指定日期穿搭失败:', err);
            reject(err);
          });
      } else {
        // 否则新建记录

        // 注意：直接上传的图片不是实际的衣物记录，所以不需要更新穿着次数
        // 如果将来直接上传的图片保存为真实衣物，可以在这里添加更新穿着次数的逻辑

        db.collection('dailyOutfits')
          .add({
            data: saveData
          })
          .then(res => {
            console.log('保存指定日期穿搭成功:', res);

            // 检查是否是跨月份操作
            const today = new Date();
            const targetYear = dateObj.getFullYear();
            const targetMonth = dateObj.getMonth() + 1;
            const currentYear = today.getFullYear();
            const currentMonth = today.getMonth() + 1;

            // 如果目标日期不是当前月份，设置跨月份穿搭添加标记
            if (targetYear !== currentYear || targetMonth !== currentMonth) {
              console.log('检测到跨月份穿搭添加，设置标记');
              wx.setStorageSync('crossMonthOutfitAdded', true);
            }

            resolve({
              success: true,
              message: '穿搭已保存',
              data: {
                ...saveData,
                _id: res._id
              }
            });

            // 设置标记，通知页面刷新
            wx.setStorageSync('needRefreshOutfits', true);
          })
          .catch(err => {
            console.error('保存指定日期穿搭失败:', err);
            reject(err);
          });
      }
    } catch (err) {
      console.error('保存指定日期穿搭过程中发生错误:', err);
      reject(err);
    }
  });
}

/**
 * 获取单个衣物数据
 * @param {string} clothingId - 衣物ID
 * @returns {Promise<Object>} 包含衣物数据的Promise
 */
function getClothingById(clothingId) {
  return new Promise((resolve, reject) => {
    if (!clothingId) {
      console.error('衣物ID不能为空');
      reject(new Error('衣物ID不能为空'));
      return;
    }

    console.log('获取衣物数据，ID:', clothingId);

    const db = wx.cloud.database();

    // 查询条件：衣物ID
    db.collection('clothes')
      .doc(clothingId)
      .get()
      .then(res => {
        console.log('获取衣物数据成功:', res.data);
        resolve(res.data);
      })
      .catch(err => {
        console.error('获取衣物数据失败:', err);
        reject(err);
      });
  });
}

/**
 * 保存直接上传的图片作为今日穿搭
 * @param {Object} imageData - 图片数据
 * @param {string} userOpenId - 用户的OpenID
 * @param {string} existingRecordId - 现有记录ID（如果要更新）
 * @param {string|Date} [targetDate] - 目标日期（可选，默认为今天）
 * @returns {Promise<Object>} 包含保存结果的Promise
 */
function saveDirectUploadAsOutfit(imageData, userOpenId, existingRecordId, targetDate) {
  return new Promise(async (resolve, reject) => {
    if (!imageData || !userOpenId) {
      console.error('图片数据或用户ID不能为空');
      reject(new Error('图片数据或用户ID不能为空'));
      return;
    }

    // 如果提供了目标日期，使用saveOutfitToDate函数
    if (targetDate) {
      console.log('使用saveOutfitToDate保存直接上传图片到指定日期:', targetDate);
      // 调用saveOutfitToDate函数，并设置isSingleClothing为true
      return saveOutfitToDate(imageData, userOpenId, targetDate, existingRecordId, true)
        .then(result => resolve(result))
        .catch(err => reject(err));
    }

    console.log('保存直接上传图片作为今日穿搭:', imageData);

    const db = wx.cloud.database();

    // 获取今天的日期
    const today = new Date();
    today.setHours(0, 0, 0, 0);

    // 准备要保存的数据
    const saveData = {
      outfitId: imageData._id || imageData.id,
      outfitName: imageData.name || '直接上传图片',
      outfitType: imageData.type || imageData.category || 'daily',
      date: today,
      createTime: db.serverDate(),
      isDirectUpload: false,
      isSingleClothing: true,
      singleClothingId: imageData._id || imageData.id,
      clothingImageFileID: imageData.imageFileID
    };

    try {
      // 如果没有提供现有记录ID，先检查今天是否已有穿搭记录
      if (!existingRecordId) {
        const _ = db.command;

        // 获取今天的结束时间（当天 23:59:59）
        const todayEnd = new Date();
        todayEnd.setHours(23, 59, 59, 999);

        console.log('查询今天的时间范围:', today, '至', todayEnd);

        // 查询今天是否已有穿搭记录（精确到当天）
        const existingRecords = await db.collection('dailyOutfits')
          .where({
            _openid: userOpenId,
            date: _.gte(today).and(_.lte(todayEnd))
          })
          .orderBy('date', 'desc')
          .limit(1)
          .get();

        if (existingRecords.data && existingRecords.data.length > 0) {
          // 如果已有记录，获取第一条记录的ID
          const record = existingRecords.data[0];
          const recordDate = new Date(record.date);

          // 验证这确实是今天的记录
          const isToday = recordDate >= today && recordDate <= todayEnd;

          if (isToday) {
            existingRecordId = record._id;
            console.log('找到今天已有穿搭记录:', existingRecordId);
          } else {
            console.warn('找到的记录不是今天的:', recordDate);
          }
        }
      }

      // 如果有现有记录，则更新
      if (existingRecordId) {
        // 准备更新数据
        const updateData = {
          outfitId: saveData.outfitId,
          outfitName: saveData.outfitName,
          outfitType: saveData.outfitType,
          updateTime: db.serverDate(),
          isDirectUpload: false,
          isSingleClothing: true,
          singleClothingId: saveData.singleClothingId,
          clothingImageFileID: saveData.clothingImageFileID,
          // 清除旧的直接上传相关字段
          directUploadImageFileID: ''
        };

        // 注意：直接上传的图片不是实际的衣物记录，所以不需要更新穿着次数
        // 如果将来直接上传的图片保存为真实衣物，可以在这里添加更新穿着次数的逻辑

        db.collection('dailyOutfits')
          .doc(existingRecordId)
          .update({
            data: updateData
          })
          .then(res => {
            console.log('更新今日穿搭成功:', res);
            resolve({
              success: true,
              message: '今日穿搭已更新',
              data: {
                ...saveData,
                _id: existingRecordId
              }
            });
          })
          .catch(err => {
            console.error('更新今日穿搭失败:', err);
            reject(err);
          });
      } else {
        // 否则新建记录

        // 注意：直接上传的图片不是实际的衣物记录，所以不需要更新穿着次数
        // 如果将来直接上传的图片保存为真实衣物，可以在这里添加更新穿着次数的逻辑

        db.collection('dailyOutfits')
          .add({
            data: saveData
          })
          .then(res => {
            console.log('保存今日穿搭成功:', res);
            resolve({
              success: true,
              message: '今日穿搭已保存',
              data: {
                ...saveData,
                _id: res._id
              }
            });
          })
          .catch(err => {
            console.error('保存今日穿搭失败:', err);
            reject(err);
          });
      }
    } catch (err) {
      console.error('保存今日穿搭过程中发生错误:', err);
      reject(err);
    }
  });
}

// 导出模块接口
module.exports = {
  getAllOutfits,
  getTodayOutfit,
  getOutfitByDate,
  saveTodayOutfit,
  saveOutfitToDate,
  getCompleteOutfitData,
  ensureDefaultImages,
  processAllOutfitsImageUrls,
  formatDate,
  generateMockOutfits,
  updateClothingWearStats,
  getClothingById,
  getClothesData,
  saveDirectUploadAsOutfit
};