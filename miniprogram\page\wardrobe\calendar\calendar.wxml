<!--page/wardrobe/calendar/calendar.wxml-->
<view class="container {{themeStyle}}" style="background-color: {{pageStyle.backgroundColor}}; background-image: {{pageStyle.backgroundImage}};">
  <!-- 状态栏占位符 -->
  <view class="status-bar-placeholder"></view>

  <!-- 顶部区域 -->
  <view class="top-section">
    <!-- 左侧返回按钮 -->
    <view class="left-section">
      <view class="back-btn" bindtap="navigateBack">
        <text class="back-icon">←</text>
      </view>
    </view>

    <!-- 中间今天按钮 -->
    <view class="center-section">
      <view class="today-btn" bindtap="jumpToToday">今天</view>
    </view>

    <!-- 右侧标题 -->
    <view class="right-section">
      <view class="section-title" style="color: {{themeStyle === 'autumn' ? colors.cowhideCocoa : (themeStyle === 'pinkBlue' ? pinkBlueColors.pinkDark : blackWhiteColors.black)}}">穿搭日历</view>
    </view>
  </view>

  <!-- 日历头部 - 年月选择 -->
  <view class="calendar-header" style="background-color: {{pageStyle.calendarHeaderBg}};">
    <view class="month-selector">
      <view class="arrow" bindtap="prevMonth">◀</view>
      <view class="current-month">{{year}}年{{month}}月</view>
      <view class="arrow" bindtap="nextMonth">▶</view>
    </view>
  </view>

  <!-- 星期栏 -->
  <view class="weekdays" style="background-color: {{pageStyle.calendarWeekdaysBg}};">
    <block wx:for="{{weekDays}}" wx:key="index">
      <view class="weekday">{{item}}</view>
    </block>
  </view>

  <!-- 日历主体 -->
  <view class="calendar-body">
    <view class="days-grid">
      <!-- 填充日期前的空白 -->
      <block wx:for="{{firstDayOfMonth}}" wx:key="index">
        <view class="day-cell empty"></view>
      </block>

      <!-- 日期单元格 -->
      <block wx:for="{{days}}" wx:key="date">
        <view class="day-cell {{item.isToday ? 'today' : ''}} {{item.hasOutfit ? 'has-outfit' : ''}} {{selectedDay === item.day ? 'selected' : ''}}"
              bindtap="selectDate" data-date="{{item.date}}">
          <view class="day-number {{item.isToday ? 'today-number' : ''}}">{{item.day}}</view>
          <!-- 穿搭标记 -->
          <view wx:if="{{item.hasOutfit}}" class="outfit-marker"></view>
        </view>
      </block>
    </view>
  </view>

  <!-- 选中日期的穿搭展示 -->
  <view class="outfit-detail-section" wx:if="{{selectedDateOutfit}}">
    <view class="date-header">{{selectedDateFormatted}} 的穿搭</view>
    <view class="outfit-detail-card" style="background-color: {{themeStyle === 'autumn' ? 'rgba(232, 209, 167, 0.2)' : 'rgba(203, 224, 249, 0.2)'}};">
      <view class="outfit-detail-header">
        <text class="outfit-detail-title">{{selectedDateOutfit.outfitName}}</text>
        <text class="outfit-detail-type" style="background-color: {{themeStyle === 'autumn' ? colors.toastedCaramel : pinkBlueColors.pinkMedium}};">{{selectedDateOutfit.outfitType}}</text>
        <text wx:if="{{selectedDateOutfit.isSingleClothing}}" class="outfit-single-clothing-badge" style="background-color: {{themeStyle === 'autumn' ? colors.spicedWine : pinkBlueColors.pinkDark}};">单件衣物</text>
        <text wx:if="{{selectedDateOutfit.isMultipleClothings}}" class="outfit-multiple-clothing-badge" style="background-color: {{themeStyle === 'autumn' ? colors.spicedWine : pinkBlueColors.pinkDark}};">组合穿搭</text>
      </view>

      <view class="outfit-content-layout">
        <!-- 多件衣物展示 -->
        <view wx:if="{{selectedDateOutfit.isMultipleClothings}}" class="multiple-clothing-display">
          <scroll-view scroll-x="true" class="multiple-clothing-scroll">
            <block wx:for="{{selectedDateOutfit.clothingItems}}" wx:key="id" wx:for-item="clothingItem">
              <view class="multiple-clothing-item">
                <image
                  class="multiple-clothing-image"
                  src="{{clothingItem.imageUrl || '/image/short-dress.png'}}"
                  mode="aspectFit"
                  lazy-load="true"
                  show-menu-by-longpress="true"
                  bindtap="previewClothingImage"
                  data-url="{{clothingItem.imageUrl}}"
                ></image>
                <view class="multiple-clothing-name">{{clothingItem.name || '未命名'}}</view>
                <view class="multiple-clothing-category">{{clothingItem.category || '未分类'}}</view>
              </view>
            </block>
          </scroll-view>
        </view>

        <!-- 单件衣物或常规穿搭图片 -->
        <view wx:else class="outfit-detail-image-container">
          <image
            class="outfit-detail-image"
            src="{{selectedDateOutfit.imageUrl || (selectedDateOutfit.isSingleClothing ? '/image/short-dress.png' : '/image/outfit-icon.png')}}"
            mode="aspectFit"
            lazy-load="true"
            show-menu-by-longpress="true"
            bindtap="previewOutfit"
          ></image>
        </view>
      </view>

      <!-- 操作按钮 -->
      <view class="outfit-actions">
        <view class="button-container">
          <button class="action-btn detail-btn" bindtap="viewOutfitDetail">查看详情</button>
        </view>
        <view class="button-container">
          <button class="action-btn select-btn" bindtap="selectAsToday">重新选择该天穿搭</button>
        </view>
        <view class="button-container">
          <button class="action-btn copy-btn" bindtap="copyToToday">复制为今日穿搭</button>
        </view>
      </view>
    </view>
  </view>

  <!-- 无穿搭提示 -->
  <view class="no-outfit-tip" wx:if="{{!selectedDateOutfit && selectedDay}}">
    <text>该日期暂无穿搭记录</text>
    <button class="add-outfit-btn" bindtap="navigateToAddOutfit">添加穿搭</button>
  </view>

  <!-- 调试按钮（仅开发环境） -->
  <view class="debug-section" wx:if="{{debugMode}}">
    <button class="debug-btn" bindtap="debugQueryMay26">调试查询5月26日</button>
    <button class="debug-btn" bindtap="debugCheckCalendarData">检查日历数据</button>
    <button class="debug-btn" bindtap="clearCacheAndRefresh">清除缓存并刷新</button>
  </view>
</view>