# 跨月份穿搭加载问题修复测试

## 问题描述
当用户在6月份添加5月份的穿搭时，这些穿搭不会被加载和显示出来。

## 根本原因
1. **缓存机制问题**：日历页面为每个月份创建独立的缓存
2. **查询范围限制**：`getMonthlyOutfits` 函数只查询当前月份的穿搭记录
3. **跨月份添加穿搭**：当用户在6月份添加5月份的穿搭时，这个穿搭记录被保存到数据库，但6月份的缓存不会包含5月份的数据
4. **缓存优先**：下次加载6月份时，系统优先使用缓存，不会重新查询数据库

## 解决方案

### 1. 添加缓存刷新检测机制
在 `calendar.js` 中添加了 `checkIfNeedRefreshCache` 函数，检测以下情况：
- 全局刷新标记 (`needRefreshOutfits`)
- 缓存过期（超过1小时）
- 跨月份穿搭添加标记 (`crossMonthOutfitAdded`)

### 2. 修改缓存清理逻辑
在 `clearAllRelatedCaches` 函数中：
- 检测目标日期是否跨月份
- 清除相关月份的缓存
- 设置跨月份穿搭添加标记

### 3. 修改保存穿搭逻辑
在 `outfitManager.js` 的 `saveOutfitToDate` 函数中：
- 检测保存的日期是否跨月份
- 如果是跨月份操作，设置 `crossMonthOutfitAdded` 标记

### 4. 增强缓存清理机制（最新修复）
添加了 `clearAllMonthlyOutfitsCache` 函数：
- 当检测到跨月份穿搭添加时，清除所有月份的缓存
- 确保所有相关月份都能获取到最新数据
- 避免因标记清除时机问题导致的缓存不一致

## 修改的文件

### miniprogram/page/wardrobe/calendar/calendar.js
1. 修改 `getMonthlyOutfits` 函数，添加缓存刷新检测
2. 添加 `checkIfNeedRefreshCache` 函数
3. 修改 `clearAllRelatedCaches` 函数，支持跨月份检测
4. 修改 `copyToToday` 函数调用，传入目标日期
5. **新增** `clearAllMonthlyOutfitsCache` 函数，清除所有月份缓存
6. **修改** `checkIfNeedRefreshCache` 函数，检测到跨月份穿搭时清除所有缓存

### miniprogram/packageOutfit/pages/calendar/calendar.js
1. **同样的修改**：为保持一致性，对这个文件进行了相同的修改
2. **新增** `clearAllMonthlyOutfitsCache` 函数
3. **修改** `checkIfNeedRefreshCache` 函数

### miniprogram/page/wardrobe/ootd/modules/outfitManager.js
1. 修改 `saveOutfitToDate` 函数，添加跨月份检测
2. 在保存和更新穿搭时设置跨月份标记

## 测试场景

### 场景1：在6月份添加5月份的穿搭
1. 用户在6月份日历页面
2. 选择5月份的某一天
3. 添加穿搭
4. 系统应该设置 `crossMonthOutfitAdded` 标记
5. 下次访问5月份日历时，应该重新从数据库加载数据

### 场景2：复制穿搭到今天（跨月份）
1. 用户在5月份日历页面
2. 选择某个穿搭
3. 复制为今日穿搭（假设今天是6月份）
4. 系统应该清除相关月份缓存并设置标记

### 场景3：缓存过期自动刷新
1. 缓存超过1小时
2. 系统应该自动重新从数据库加载数据

## 预期效果
- 跨月份添加穿搭后，相关月份的缓存会被清除
- 下次访问时会重新从数据库加载最新数据
- 穿搭能够正确显示在对应的月份中
