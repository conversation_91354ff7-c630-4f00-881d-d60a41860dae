# 日历页面穿搭加载问题修复测试

## 问题描述
用户反馈在日历页面加载每月穿搭时，有一些穿搭明明在数据库中存在但是就是加载不出来。

## 修复内容

### 1. 数据库查询范围扩展
- 原来的查询范围：当月第一天00:00:00 到 当月最后一天23:59:59
- 修复后的查询范围：向前后各扩展一天，防止时区问题

### 2. 日期处理逻辑改进
- 增强了日期解析逻辑，支持多种日期格式
- 添加了目标月份验证，确保只处理当月的记录
- 改进了日期字符串生成逻辑

### 3. 调试功能增强
- 添加了 `debugQuerySpecificDate` 函数
- 添加了调试按钮，可以直接测试特定日期
- 增加了详细的调试日志

## 测试步骤

### 1. 基本功能测试
1. 打开日历页面
2. 查看是否能正常加载当月的穿搭记录
3. 点击有穿搭标记的日期，查看是否能正常显示穿搭详情

### 2. 特定问题测试
1. 导航到2025年5月
2. 查看5月26日是否显示穿搭标记
3. 点击5月26日，查看是否能正常显示穿搭详情

### 3. 调试功能测试
1. 在页面右下角应该能看到两个红色的调试按钮
2. 点击"调试查询5月26日"按钮
3. 查看控制台输出，确认是否能查询到记录
4. 点击"清除缓存并刷新"按钮
5. 查看页面是否重新加载数据

### 4. 缓存测试
1. 第一次加载页面时，数据应该从云端获取
2. 再次进入页面时，应该优先使用缓存数据
3. 清除缓存后，应该重新从云端获取数据

## 预期结果
- 所有存在于数据库中的穿搭记录都应该能在日历上正确显示
- 5月26日的穿搭记录应该能正常加载和显示
- 调试功能应该能正常工作，帮助诊断问题
- 缓存机制应该正常工作，提高页面加载速度

## 关键修改文件
- `miniprogram/page/wardrobe/calendar/calendar.js`
- `miniprogram/page/wardrobe/calendar/calendar.wxml`
- `miniprogram/page/wardrobe/calendar/calendar.wxss`

## 注意事项
- 调试按钮仅在开发环境中显示（debugMode: true）
- 生产环境中应该将 debugMode 设置为 false
- 如果问题仍然存在，可以通过调试功能查看详细的查询结果
